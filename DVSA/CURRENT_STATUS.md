# DVSA 攻击复现当前状态

## 📋 **当前配置状态**

### LocalStack 配置
- **位置**: `/home/<USER>/yuchen/rca/test/docker-compose.yml`
- **容器名**: `serverless-attack-localstack`
- **端口**: `4566`
- **服务**: `lambda,apigateway,s3,dynamodb,iam,sts,logs,events,secretsmanager,ssm`
- **执行器**: `LAMBDA_EXECUTOR=docker`
- **Docker Socket**: 已挂载 `/var/run/docker.sock:/var/run/docker.sock`

### 当前环境状态
- **SAM CLI**: ✅ 已安装 (版本 1.142.1)
- **SAM Local API**: ✅ 运行中 (http://localhost:3000)
- **Conda 环境**: ✅ 已激活 `serverless-attack`
- **AWS CLI 配置**: ✅ 已配置
- **部署状态**: ✅ DVSA 已通过 SAM Local 部署

## 🎯 **需要的文件（仅保留这些）**

### 核心文件
1. **`deploy_to_localstack.py`** - 部署脚本
2. **`control_flow_attack_suite.py`** - 攻击脚本
3. **`CURRENT_STATUS.md`** - 当前状态维护（本文件）
4. **`CONTROL_FLOW_ATTACK_README.md`** - 使用说明

### DVSA 源文件
- `backend/functions/processing/payment_processing.py`
- `backend/functions/order-manager/order-manager.js`
- `backend/functions/order/order_billing.py`

## 🚀 **正确的操作流程 (已简化)**

### 步骤 1: 部署和测试DVSA ✅
```bash
# 激活 conda 环境
conda activate serverless-attack

# 运行部署和完整测试脚本
python scripts/deploy_and_test.py
```

这个脚本会：
- 自动检测端口占用和SAM Local状态
- 使用SAM CLI构建和部署DVSA
- 验证所有26个Lambda函数
- 测试完整的购物流程（包括DynamoDB依赖）
- 支持Ctrl+C优雅中断和清理
- 生成详细的部署测试报告

### 步骤 2: 执行控制流篡改攻击 🎯
```bash
# 运行通用控制流攻击
python scripts/control_flow_attack.py

# 运行DVSA特定攻击（基于官方文档）
python scripts/dvsa_specific_attacks.py
```

**通用攻击**包含四种控制流篡改攻击：
- 事件源篡改 (Event Source Tampering)
- 函数链劫持 (Function Chain Hijacking)
- 参数污染 (Parameter Pollution)
- 外部服务依赖篡改 (External Service Dependency Tampering)

**特定攻击**基于DVSA官方文档：
- Event Injection (事件注入)
- Broken Authentication (认证绕过)
- Sensitive Information Disclosure (敏感信息泄露)
- Broken Access Control (访问控制绕过)

### 步骤 3: 查看报告 📊
```bash
# 查看生成的报告
ls report/
cat report/dvsa_deployment_test_report_*.json
cat report/dvsa_control_flow_attack_report_*.json
```

## 📝 **当前操作记录**

### 已执行的操作
1. ✅ 添加了 SAM CLI 使用教程到 tools/ 目录
2. ✅ 创建了详细的实验流程规划文档
3. ✅ 修复了 DVSA template.yml 中的 CORS 配置问题
4. ✅ 成功构建了 DVSA 应用 (`sam build`)
5. ✅ 启动了 SAM Local API (http://localhost:3000)
6. ✅ 创建并运行了 API 攻击脚本
7. ✅ 验证了 API 端点可访问性

### 当前问题
1. **Lambda 函数执行错误**: API 返回 "Internal server error"
2. **函数依赖缺失**: Python 函数可能缺少必要的依赖包
3. **认证问题**: 某些端点可能需要 Cognito 认证

### 下一步操作
1. 分析 Lambda 函数错误原因
2. 添加必要的 requirements.txt 文件
3. 设计绕过认证的攻击方法
4. 完善攻击脚本和报告生成

## 🔧 **问题解决记录**

### AWS CLI 区域错误
**问题**: `You must specify a region`
**原因**: 未设置 AWS_DEFAULT_REGION 环境变量
**解决**: `export AWS_DEFAULT_REGION=us-east-1`

### LocalStack 连接问题
**状态**: 待检查
**可能原因**: LocalStack 未运行或配置问题
**解决方案**: 使用现有的 docker-compose.yml 启动

## 📊 **部署配置详情**

### Lambda 函数配置
- **Runtime**: Python 3.9 (payment_processing.py, order_billing.py)
- **Runtime**: Node.js 18.x (order-manager.js)
- **Role**: `arn:aws:iam::123456789012:role/lambda-role`
- **Timeout**: 30 秒

### 函数名称映射
- `dvsa-payment-processing` ← `backend/functions/processing/payment_processing.py`
- `dvsa-order-manager` ← `backend/functions/order-manager/order-manager.js`
- `dvsa-order-billing` ← `backend/functions/order/order_billing.py`

## 🎯 **攻击目标**

### 主要漏洞
1. **支付处理函数直接调用** - 可绕过订单流程暴力破解信用卡
2. **订单状态操控** - 可直接修改订单状态
3. **账单金额篡改** - 可创建零金额或负金额账单

### 攻击类型
- 支付绕过攻击
- 信用卡暴力破解
- 订单流程操控
- 账单处理绕过

---

**更新时间**: 每次操作后更新此文件
**维护原则**: 保持简洁，只记录关键信息和当前状态
