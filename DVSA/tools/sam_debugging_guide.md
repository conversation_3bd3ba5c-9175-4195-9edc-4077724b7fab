# SAM CLI 调试和函数管理指南

## 📋 **查看部署的函数（类似kubectl）**

### 1. 查看所有函数定义
```bash
# 查看template.yml中定义的所有函数
sam list resources --stack-name dvsa

# 或者直接查看模板中的函数
grep -A 5 "Type.*AWS::Serverless::Function" template.yml
```

### 2. 查看API端点映射
```bash
# 查看所有API端点
sam list endpoints --stack-name dvsa

# 或者查看当前运行的端点（从SAM Local输出）
# 当运行 sam local start-api 时会显示：
# Mounting PaymentProcessorFunction at http://127.0.0.1:3000/payment [POST, OPTIONS]
# Mounting OrderManagerFunction at http://127.0.0.1:3000/order [POST, OPTIONS]
# 等等...
```

### 3. 测试单个函数
```bash
# 直接调用单个函数（不通过API Gateway）
sam local invoke PaymentProcessorFunction --event test_events/payment_event.json

# 生成测试事件
sam local generate-event apigateway aws-proxy > test_payment_event.json
```

### 4. 查看函数日志
```bash
# SAM Local的日志直接显示在终端中
# 每次函数调用都会显示：
# - START RequestId: xxx
# - 函数内部的print/console.log输出
# - END RequestId: xxx
# - REPORT RequestId: xxx (包含执行时间、内存使用等)

# 如果需要保存日志到文件：
sam local start-api --port 3000 > sam_api.log 2>&1 &
```

## 🔍 **函数状态检查命令**

### 检查函数构建状态
```bash
# 验证模板语法
sam validate

# 查看构建输出
ls -la .aws-sam/build/

# 查看特定函数的构建内容
ls -la .aws-sam/build/PaymentProcessorFunction/
```

### 检查函数依赖
```bash
# 检查Python函数依赖
find .aws-sam/build/ -name "requirements.txt" -exec cat {} \;

# 检查Node.js函数依赖
find .aws-sam/build/ -name "package.json" -exec cat {} \;
```

## 🚀 **函数测试工作流**

### 1. 基础连通性测试
```bash
# 测试API Gateway根路径
curl http://localhost:3000/

# 测试OPTIONS请求（CORS预检）
curl -X OPTIONS http://localhost:3000/payment
```

### 2. 逐个函数测试
```bash
# 支付处理函数
curl -X POST http://localhost:3000/payment \
  -H "Content-Type: application/json" \
  -d '{"ccn":"4532015112830366","exp":"12/25","cvv":"123"}'

# 购物车总计函数
curl -X POST http://localhost:3000/total \
  -H "Content-Type: application/json" \
  -d '{"cartId":"test-cart-123"}'

# 订单管理函数（需要认证）
curl -X POST http://localhost:3000/order \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer fake-token" \
  -d '{"action":"new","cart-id":"test-cart","items":[{"id":"item1","quantity":1}]}'

# 管理员函数
curl -X POST http://localhost:3000/admin \
  -H "Content-Type: application/json" \
  -d '{"action":"test"}'
```

## 📊 **日志分析**

### 常见错误类型
1. **KeyError**: 缺少必需参数
2. **TypeError**: 参数类型错误
3. **ImageBuildException**: Docker镜像构建失败
4. **Internal server error**: 函数内部逻辑错误

### 日志解读示例
```
START RequestId: xxx Version: $LATEST
[ERROR] KeyError: 'ccn'
Traceback (most recent call last):
  File "/var/task/payment_processing.py", line 29, in lambda_handler
    ccn = data['ccn']
END RequestId: xxx
REPORT RequestId: xxx Duration: 4125.56 ms Memory Size: 128 MB Max Memory Used: 128 MB
```

这表示：
- 函数启动成功
- 在第29行出现KeyError，缺少'ccn'参数
- 执行时间4.1秒，内存使用正常

## 🛠 **故障排除**

### 1. 函数无响应
```bash
# 检查端口占用
netstat -tlnp | grep 3000

# 重启SAM Local
pkill -f "sam local"
sam local start-api --port 3000
```

### 2. Docker相关问题
```bash
# 检查Docker状态
docker ps
docker images | grep lambda

# 清理Docker缓存
docker system prune -f
```

### 3. 依赖问题
```bash
# 重新构建
sam build --use-container

# 强制重新构建
rm -rf .aws-sam/
sam build
```

## 📝 **监控脚本示例**

创建一个监控脚本来持续检查函数状态：

```bash
#!/bin/bash
# monitor_functions.sh

echo "=== DVSA函数状态监控 ==="
echo "时间: $(date)"

endpoints=("/payment" "/total" "/order" "/admin")

for endpoint in "${endpoints[@]}"; do
    echo "测试 $endpoint ..."
    response=$(curl -s -o /dev/null -w "%{http_code}" \
        -X POST http://localhost:3000$endpoint \
        -H "Content-Type: application/json" \
        -d '{}' \
        --max-time 5)
    
    if [ "$response" = "200" ] || [ "$response" = "400" ] || [ "$response" = "500" ]; then
        echo "  ✅ $endpoint: HTTP $response (函数可达)"
    else
        echo "  ❌ $endpoint: HTTP $response (函数不可达)"
    fi
done
```

## 🎯 **下一步操作建议**

1. **先运行基础测试**：确保所有端点都能响应
2. **分析函数源码**：了解每个函数的参数要求
3. **创建正确的测试数据**：基于源码分析结果
4. **逐个验证函数**：确保所有函数都能正常工作
5. **最后进行攻击测试**：在确认函数正常后进行安全测试
