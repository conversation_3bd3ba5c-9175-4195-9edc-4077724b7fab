{"timestamp": "2025-07-14T14:21:31.288318", "tests": [{"function_name": "PaymentProcessor", "endpoint": "/payment", "method": "POST", "test_cases": [{"test_name": "有效支付数据", "status_code": 502, "response_body": "{\"message\":\"Internal server error\"}\n", "success": false, "response_time": 0.540889}, {"test_name": "无效信用卡号", "status_code": 502, "response_body": "{\"message\":\"Internal server error\"}\n", "success": false, "response_time": 0.468774}, {"test_name": "过期日期", "status_code": 502, "response_body": "{\"message\":\"Internal server error\"}\n", "success": false, "response_time": 1.376459}, {"test_name": "无效CVV", "status_code": 502, "response_body": "{\"message\":\"Internal server error\"}\n", "success": false, "response_time": 0.86693}], "success_rate": "0/4"}, {"function_name": "GetCartTotal", "endpoint": "/total", "method": "POST", "test_cases": [{"test_name": "简单购物车数组格式", "status_code": 502, "response_body": "{\"message\":\"Internal server error\"}\n", "success": false, "response_time": 1.69248}, {"test_name": "购物车字典格式", "status_code": 502, "response_body": "{\"message\":\"Internal server error\"}\n", "success": false, "response_time": 1.419627}, {"test_name": "空购物车", "status_code": 200, "response_body": "{\"status\": \"ok\", \"total\": 0.0, \"missing\": {}}", "success": true, "response_time": 6.169071}], "success_rate": "1/3"}, {"function_name": "OrderManager", "endpoint": "/order", "method": "POST", "test_cases": [{"test_name": "无认证请求", "status_code": 502, "response_body": "{\"message\":\"Internal server error\"}\n", "success": false, "response_time": 2.019331}], "success_rate": "0/1"}, {"function_name": "AdminShell", "endpoint": "/admin", "method": "POST", "test_cases": [{"test_name": "基础管理员请求", "status_code": 500, "response_body": "{\"message\":\"Internal server error\"}\n", "success": true, "response_time": 6.134217}], "success_rate": "1/1"}], "summary": {"total_functions_tested": 4, "total_test_cases": 9, "successful_test_cases": 2, "success_rate": "22.2%"}}