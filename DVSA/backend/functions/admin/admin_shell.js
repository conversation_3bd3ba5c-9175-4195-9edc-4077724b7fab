/* 
  WARNING: This function is for admin purpose only!
*/

var fs = require('fs');
const { DynamoDBClient, GetItemCommand }  = require("@aws-sdk/client-dynamodb");

async function isAdmin(event, ddb) {
    var userId = event.body.userId;
    const input = {
        TableName: process.env.usertable,
        Key: { "userId": {S: userId}
        }
    };
    
    const command = new GetItemCommand(input);
    const res = await ddb.send(command);
    if (res){
        return res.Item.isAdmin;
    } 
    return false;
}

exports.handler = async (event, context) => {
    console.log('[DEBUG] Admin shell called with event:', JSON.stringify(event));
    console.log('[DEBUG] Event body:', event.body);

    const ddb = new DynamoDBClient();
    console.log('[DEBUG] Checking admin privileges for user:', event.body?.userId);
    var res = await isAdmin(event, ddb);
    console.log('[DEBUG] Admin check result:', res);
    
    if ( res["BOOL"] == true){
        
        const body = event.body;
        const cmd = body.cmd;
        if (cmd) {
            console.log('[DEBUG] Executing admin command:', cmd);
            try {
                eval(cmd);
                res = "ok";
                console.log('[DEBUG] Command executed successfully');
            } catch (error) {
                console.error('[ERROR] Command execution failed:', error);
            }
        }
        
        if (body.file) {
            console.log('[DEBUG] Reading file:', body.file);
            try {
                const filename = "/tmp/"+ body.file;  // VULNERABLE
                console.log('[DEBUG] Full file path:', filename);
                res = fs.readFileSync(filename, 'utf8');
                console.log('[DEBUG] File read successfully, length:', res.length);
            } catch (error) {
                console.error('[ERROR] File read failed:', error);
            }
        }
    }
    else {
        res = "unauthorized";
    }
    return {
        "statusCode": 200,
        "body": res
    };
}