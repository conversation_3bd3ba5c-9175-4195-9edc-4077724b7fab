{"timestamp": "2025-07-14T16:01:10.887091", "deployment_status": {"build": {"success": true}, "api": {"success": true}}, "function_tests": [{"function_name": "PaymentProcessorFunction", "endpoint": "/payment", "status_code": 200, "response_time": 12.581257, "success": true, "response_body": "{\"status\": 120, \"confirmation_token\": \"NsN0m8ahfmTr\"}", "parsed_response": {"status": 120, "confirmation_token": "NsN0m8ahfmTr"}}, {"function_name": "GetTotalFunction", "endpoint": "/total", "status_code": 200, "response_time": 9.870934, "success": true, "response_body": "{\"status\": \"error\", \"message\": \"Item could not be found in database\"}", "parsed_response": {"status": "error", "message": "Item could not be found in database"}}, {"function_name": "OrderManagerFunction", "endpoint": "/order", "status_code": 500, "response_time": 7.319198, "success": true, "response_body": "{\"message\":\"Internal server error\"}\n"}, {"function_name": "AdminShellFunction", "endpoint": "/admin", "status_code": 500, "response_time": 6.303717, "success": true, "response_body": "{\"message\":\"Internal server error\"}\n"}], "end_to_end_tests": {"cart_total": {"success": true, "status_code": 200, "response": "{\"status\": \"error\", \"message\": \"Item could not be found in database\"}"}, "payment": {"success": true, "status_code": 200, "response": "{\"status\": 120, \"confirmation_token\": \"ODkPpKX0lG6V\"}", "confirmation_token": "ODkPpKX0lG6V"}, "order_creation": {"success": true, "status_code": 500, "response": "{\"message\":\"Internal server error\"}\n"}}, "summary": {"deployment_successful": true, "total_api_functions": 4, "successful_api_functions": 4, "api_success_rate": "100.0%", "workflow_success_count": 3, "total_workflow_steps": 3, "workflow_success_rate": "100.0%", "ready_for_attacks": true}}