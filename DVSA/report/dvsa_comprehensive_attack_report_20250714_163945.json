{"timestamp": "2025-07-14T16:33:10.385533", "target_system": "DVSA (Damn Vulnerable Serverless Application)", "function_coverage_test": {"api_functions": {"payment": {"reachable": true, "status_code": 200, "function_name": "DVSA-PAYMENT-PROCESSOR"}, "total": {"reachable": true, "status_code": 200, "function_name": "DVSA-GET-CART-TOTAL"}, "order": {"reachable": true, "status_code": 500, "function_name": "DVSA-ORDER-MANAGER"}, "admin": {"reachable": true, "status_code": 500, "function_name": "DVSA-ADMIN-SHELL"}}, "internal_functions": {"DVSA-ORDER-NEW": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'items'\", \"errorType\": \"KeyError\", \"requestId\": \"c86be15d-b13c-42ef-9c3f-26e60dfbe54c\", \"stackTrace\": [\"  File \\\"/var/task/new_order.py\\\", line 9, in lambda_handler\\n    itemList = e", "stderr": "No current session found, using default AWS::AccountId\nInvoking new_order.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\nMoun"}, "DVSA-ORDER-CANCEL": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'orderId'\", \"errorType\": \"KeyError\", \"requestId\": \"cd0f816e-7765-49ad-8097-fd699ae5be35\", \"stackTrace\": [\"  File \\\"/var/task/cancel_order.py\\\", line 18, in lambda_handler\\n    orderI", "stderr": "No current session found, using default AWS::AccountId\nInvoking cancel_order.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\nM"}, "DVSA-ORDER-GET": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'orderId'\", \"errorType\": \"KeyError\", \"requestId\": \"5b70f554-4c1a-4822-9370-8e1dfddf1cd7\", \"stackTrace\": [\"  File \\\"/var/task/get_order.py\\\", line 30, in lambda_handler\\n    orderId =", "stderr": "No current session found, using default AWS::AccountId\nInvoking get_order.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\nMoun"}, "DVSA-ORDER-ORDERS": {"reachable": false, "error": "Command '['sam', 'local', 'invoke', 'DVSA-ORDER-ORDERS', '--event', '/tmp/test_DVSA_ORDER_ORDERS.json']' timed out after 15 seconds"}, "DVSA-ORDER-SHIPPING": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'orderId'\", \"errorType\": \"KeyError\", \"requestId\": \"210a177b-a35c-4d3d-a8f6-b0cef0996934\", \"stackTrace\": [\"  File \\\"/var/task/order_shipping.py\\\", line 18, in lambda_handler\\n    orde", "stderr": "No current session found, using default AWS::AccountId\nInvoking order_shipping.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n"}, "DVSA-ORDER-UPDATE": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'orderId'\", \"errorType\": \"KeyError\", \"requestId\": \"c47c799a-8a42-4fb9-92e3-4c255412d505\", \"stackTrace\": [\"  File \\\"/var/task/update_order.py\\\", line 17, in lambda_handler\\n    orderI", "stderr": "No current session found, using default AWS::AccountId\nInvoking update_order.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\nM"}, "DVSA-ORDER-BILLING": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'orderId'\", \"errorType\": \"KeyError\", \"requestId\": \"903955d3-c790-4d2c-afd4-65ba8cb41715\", \"stackTrace\": [\"  File \\\"/var/task/order_billing.py\\\", line 34, in lambda_handler\\n    order", "stderr": "No current session found, using default AWS::AccountId\nInvoking order_billing.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\n"}, "DVSA-ORDER-COMPLETE": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'orderId'\", \"errorType\": \"KeyError\", \"requestId\": \"30cb1fcf-3251-4b38-b723-343465bf988f\", \"stackTrace\": [\"  File \\\"/var/task/order_complete.py\\\", line 33, in lambda_handler\\n    orde", "stderr": "No current session found, using default AWS::AccountId\nInvoking order_complete.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n"}, "DVSA-CREATE-RECEIPT": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'Records'\", \"errorType\": \"KeyError\", \"requestId\": \"ea1d924c-7ee5-455c-8b47-f297d48b3387\", \"stackTrace\": [\"  File \\\"/var/task/create_receipt.py\\\", line 50, in lambda_handler\\n    data", "stderr": "No current session found, using default AWS::AccountId\nInvoking create_receipt.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n"}, "DVSA-SEND-RECEIPT-EMAIL": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'Records'\", \"errorType\": \"KeyError\", \"requestId\": \"bb58719b-b5fa-4d30-9c5c-d6493e61f2a1\", \"stackTrace\": [\"  File \\\"/var/task/send_receipt_email.py\\\", line 21, in lambda_handler\\n    ", "stderr": "No current session found, using default AWS::AccountId\nInvoking send_receipt_email.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_"}, "DVSA-FEEDBACK-UPLOADS": {"reachable": true, "return_code": 0, "stdout": "{\"status\": \"ok\", \"message\": \"Thank you.\"}", "stderr": "No current session found, using default AWS::AccountId\nInvoking feedback_uploads.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64"}, "DVSA-CRON-PROCESSOR": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"An error occurred (UnrecognizedClientException) when calling the Scan operation: The security token included in the request is invalid.\", \"errorType\": \"ClientError\", \"requestId\": \"2b", "stderr": "No current session found, using default AWS::AccountId\nInvoking cron_processor.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n"}, "DVSA-CRON-ORDER-CLEANER": {"reachable": false, "error": "Command '['sam', 'local', 'invoke', 'DVSA-CRON-ORDER-CLEANER', '--event', '/tmp/test_DVSA_CRON_ORDER_CLEANER.json']' timed out after 15 seconds"}, "DVSA-CRON-JOB-UPDATE": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"An error occurred (UnrecognizedClientException) when calling the Scan operation: The security token included in the request is invalid.\", \"errorType\": \"ClientError\", \"requestId\": \"81", "stderr": "No current session found, using default AWS::AccountId\nInvoking cron_update.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\nMo"}, "DVSA-USER-CREATE": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'userName'\", \"errorType\": \"KeyError\", \"requestId\": \"e2a7684a-f7e5-4a22-a4a6-021a804d18ee\", \"stackTrace\": [\"  File \\\"/var/task/user_create.py\\\", line 9, in lambda_handler\\n    userId ", "stderr": "No current session found, using default AWS::AccountId\nInvoking user_create.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\nMo"}, "DVSA-USER-ACCOUNT": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'user'\", \"errorType\": \"KeyError\", \"requestId\": \"ca06e624-d0d5-4d9b-84a9-e32d5ed22644\", \"stackTrace\": [\"  File \\\"/var/task/user_account.py\\\", line 8, in lambda_handler\\n    userId = e", "stderr": "No current session found, using default AWS::AccountId\nInvoking user_account.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\nM"}, "DVSA-USER-PROFILE": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'user'\", \"errorType\": \"KeyError\", \"requestId\": \"33073e2b-3dd7-4a24-ac74-7c9b79adcfa5\", \"stackTrace\": [\"  File \\\"/var/task/user_profile.py\\\", line 6, in lambda_handler\\n    userId = e", "stderr": "No current session found, using default AWS::AccountId\nInvoking user_profile.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\nM"}, "DVSA-USER-INBOX": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'action'\", \"errorType\": \"KeyError\", \"requestId\": \"934e72b3-bd86-4879-a4ed-fd1b9d2c4614\", \"stackTrace\": [\"  File \\\"/var/task/user_inbox.py\\\", line 52, in lambda_handler\\n    action = ", "stderr": "No current session found, using default AWS::AccountId\nInvoking user_inbox.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\nMou"}, "DVSA-ADMIN-GET-ORDERS": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"An error occurred (UnrecognizedClientException) when calling the Scan operation: The security token included in the request is invalid.\", \"errorType\": \"ClientError\", \"requestId\": \"c0", "stderr": "No current session found, using default AWS::AccountId\nInvoking admin_get_orders.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64"}, "DVSA-ADMIN-GET-RECEIPT": {"reachable": false, "error": "Command '['sam', 'local', 'invoke', 'DVSA-ADMIN-GET-RECEIPT', '--event', '/tmp/test_DVSA_ADMIN_GET_RECEIPT.json']' timed out after 15 seconds"}, "DVSA-ADMIN-UPDATE-ORDERS": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'headers'\", \"errorType\": \"KeyError\", \"requestId\": \"545888f7-5202-49ca-8ba6-e7af58e988b6\", \"stackTrace\": [\"  File \\\"/var/task/admin_update_orders.py\\\", line 73, in lambda_handler\\n   ", "stderr": "No current session found, using default AWS::AccountId\nInvoking admin_update_orders.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86"}, "DVSA-INIT": {"reachable": true, "return_code": 0, "stdout": "{\"errorMessage\": \"'ResponseURL'\", \"errorType\": \"KeyError\", \"requestId\": \"f0ff9d14-59e0-4781-b191-70c99a2b1bb5\", \"stackTrace\": [\"  File \\\"/var/task/dvsa_init.py\\\", line 23, in lambda_handler\\n    cf_ob", "stderr": "No current session found, using default AWS::AccountId\nInvoking dvsa_init.lambda_handler (python3.12)\nLocal image is up-to-date\nUsing local image: public.ecr.aws/lambda/python:3.12-rapid-x86_64.\n\nMoun"}}, "total_functions": 26, "reachable_functions": 23, "unreachable_functions": 3}, "specific_attacks": [{"attack_name": "Event Injection", "attack_type": "Deserialization Vulnerability", "target_function": "DVSA-ORDER-MANAGER", "attempts": [{"test_name": "简化代码执行测试", "status_code": 500, "response": "{\"message\":\"Internal server error\"}\n", "success": true}, {"test_name": "Lambda函数调用测试", "status_code": 500, "response": "{\"message\":\"Internal server error\"}\n", "success": true}], "vulnerabilities_found": [{"type": "反序列化代码注入", "description": "注入payload导致函数错误: 简化代码执行测试", "evidence": "{\"message\":\"Internal server error\"}\n", "severity": "CRITICAL", "rca_applicable": true}, {"type": "反序列化代码注入", "description": "注入payload导致函数错误: Lambda函数调用测试", "evidence": "{\"message\":\"Internal server error\"}\n", "severity": "CRITICAL", "rca_applicable": true}], "success_count": 2}, {"attack_name": "Broken Authentication", "attack_type": "Credit Card Brute Force", "target_function": "DVSA-PAYMENT-PROCESSOR", "attempts": [], "vulnerabilities_found": [], "success_count": 0}, {"attack_name": "Sensitive Information Disclosure", "attack_type": "Admin Data Access", "target_function": "DVSA-ORDER-MANAGER -> Admin Functions", "attempts": [], "vulnerabilities_found": [{"type": "管理员函数访问尝试", "description": "尝试通过注入访问管理员函数", "evidence": "{\"message\":\"Internal server error\"}\n", "severity": "HIGH", "rca_applicable": true}], "success_count": 1}, {"attack_name": "Broken Access Control", "attack_type": "Admin Function Access", "target_function": "DVSA-ADMIN-SHELL", "attempts": [], "vulnerabilities_found": [{"type": "管理员功能访问尝试", "description": "尝试执行管理员操作: 命令执行测试", "evidence": "{\"message\":\"Internal server error\"}\n", "severity": "HIGH", "rca_applicable": true}, {"type": "管理员功能访问尝试", "description": "尝试执行管理员操作: 文件读取测试", "evidence": "{\"message\":\"Internal server error\"}\n", "severity": "HIGH", "rca_applicable": true}], "success_count": 2}], "control_flow_attacks": [], "summary": {"function_coverage": {"total_functions": 26, "reachable_functions": 23, "coverage_rate": "88.5%"}, "attack_results": {"total_attack_types": 4, "successful_attack_types": 3, "attack_success_rate": "75.0%", "total_vulnerabilities_found": 5, "rca_applicable_vulnerabilities": 5}, "rca_analysis": {"applicable": true, "recommended_focus": ["Deserialization Vulnerability", "Admin Data Access", "Admin Function Access"], "severity_distribution": {"CRITICAL": 2, "HIGH": 3, "MEDIUM": 0}}, "deployment_readiness": {"functions_working": true, "attacks_possible": true, "ready_for_rca": true}}}