#!/usr/bin/env python3
"""
DVSA 部署和完整流程测试脚本
功能：
1. 使用SAM CLI部署DVSA到本地环境
2. 验证所有26个Lambda函数正常工作（包括内部函数）
3. 测试完整的购物流程（包括DynamoDB等依赖）
4. 分析500错误原因并提供修复建议
5. 生成详细的部署和测试报告
"""

import subprocess
import requests
import json
import time
import os
import signal
import sys
import socket
import boto3
from datetime import datetime
from typing import Dict, List, Any

class DVSADeploymentTester:
    def __init__(self):
        self.base_url = "http://localhost:3000"
        self.port = 3000
        self.api_process = None
        self.interrupted = False
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "deployment_status": {},
            "api_function_tests": [],
            "internal_function_tests": [],
            "end_to_end_tests": [],
            "error_analysis": [],
            "summary": {}
        }

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        # 初始化函数定义
        self._init_functions()

    def _signal_handler(self, signum, frame):
        """处理Ctrl+C等中断信号"""
        print(f"\n⏹️ 收到中断信号 ({signum})，正在清理...")
        self.interrupted = True
        self.cleanup()
        sys.exit(0)

    def _check_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result != 0  # 0表示端口被占用
        except Exception:
            return True  # 如果检查失败，假设端口可用

    def _find_sam_process(self) -> bool:
        """检查是否已有SAM Local进程在运行"""
        try:
            # 检查端口是否被占用
            if not self._check_port_available(self.port):
                # 尝试连接看是否是SAM Local
                try:
                    response = requests.get(f"{self.base_url}/", timeout=2)
                    if response.status_code == 403:  # SAM Local的典型响应
                        return True
                except:
                    pass
                return False  # 端口被占用但不是SAM Local
            return False
        except Exception:
            return False

    def _init_functions(self):
        """初始化DVSA函数定义"""
        # API Gateway直接路由的函数
        self.api_functions = {
            "payment": {
                "endpoint": "/payment",
                "method": "POST",
                "function_name": "DVSA-PAYMENT-PROCESSOR",
                "description": "处理支付请求",
                "test_payload": {
                    "ccn": "4532015112830366",
                    "exp": "12/25",
                    "cvv": "123"
                }
            },
            "total": {
                "endpoint": "/total",
                "method": "POST",
                "function_name": "DVSA-GET-CART-TOTAL",
                "description": "计算购物车总价",
                "test_payload": [
                    {"itemId": "item1", "quantity": 1, "price": 10.99}
                ]
            },
            "order": {
                "endpoint": "/order",
                "method": "POST",
                "function_name": "DVSA-ORDER-MANAGER",
                "description": "订单管理（需要认证）",
                "test_payload": {
                    "action": "new",
                    "cart-id": "test-cart",
                    "items": [{"id": "item1", "quantity": 1}]
                },
                "requires_auth": True
            },
            "admin": {
                "endpoint": "/admin",
                "method": "POST",
                "function_name": "DVSA-ADMIN-SHELL",
                "description": "管理员功能（需要管理员权限）",
                "test_payload": {
                    "userId": "admin-user",
                    "cmd": "console.log('test')"
                },
                "requires_admin": True
            }
        }

        # 内部Lambda函数（通过Lambda调用或事件触发）
        self.internal_functions = {
            "order_new": {
                "function_name": "DVSA-ORDER-NEW",
                "description": "创建新订单",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user", "cartId": "test-cart", "items": []}
            },
            "order_cancel": {
                "function_name": "DVSA-ORDER-CANCEL",
                "description": "取消订单",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user", "orderId": "test-order"}
            },
            "order_get": {
                "function_name": "DVSA-ORDER-GET",
                "description": "获取订单详情",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user", "orderId": "test-order"}
            },
            "orders_get": {
                "function_name": "DVSA-ORDER-ORDERS",
                "description": "获取用户所有订单",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user"}
            },
            "order_shipping": {
                "function_name": "DVSA-ORDER-SHIPPING",
                "description": "更新订单配送信息",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user", "orderId": "test-order"}
            },
            "order_update": {
                "function_name": "DVSA-ORDER-UPDATE",
                "description": "更新订单信息",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user", "orderId": "test-order"}
            },
            "order_billing": {
                "function_name": "DVSA-ORDER-BILLING",
                "description": "处理订单账单",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user", "orderId": "test-order"}
            },
            "order_complete": {
                "function_name": "DVSA-ORDER-COMPLETE",
                "description": "完成订单",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user", "orderId": "test-order"}
            },
            "create_receipt": {
                "function_name": "DVSA-CREATE-RECEIPT",
                "description": "创建收据",
                "trigger": "SQS消息",
                "test_payload": {"orderId": "test-order", "amount": 100}
            },
            "send_receipt": {
                "function_name": "DVSA-SEND-RECEIPT-EMAIL",
                "description": "发送收据邮件",
                "trigger": "S3事件",
                "test_payload": {}
            },
            "feedback_upload": {
                "function_name": "DVSA-FEEDBACK-UPLOADS",
                "description": "处理反馈上传",
                "trigger": "S3事件",
                "test_payload": {}
            },
            "cron_processor": {
                "function_name": "DVSA-CRON-PROCESSOR",
                "description": "定时任务处理器",
                "trigger": "CloudWatch Events",
                "test_payload": {}
            },
            "cron_order_cleaner": {
                "function_name": "DVSA-CRON-ORDER-CLEANER",
                "description": "定时清理订单",
                "trigger": "CloudWatch Events",
                "test_payload": {}
            },
            "cron_job_update": {
                "function_name": "DVSA-CRON-JOB-UPDATE",
                "description": "定时任务更新",
                "trigger": "CloudWatch Events",
                "test_payload": {}
            },
            "user_create": {
                "function_name": "DVSA-USER-CREATE",
                "description": "创建用户",
                "trigger": "Cognito PostConfirmation",
                "test_payload": {}
            },
            "user_account": {
                "function_name": "DVSA-USER-ACCOUNT",
                "description": "用户账户管理",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user"}
            },
            "user_profile": {
                "function_name": "DVSA-USER-PROFILE",
                "description": "用户资料管理",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user"}
            },
            "user_inbox": {
                "function_name": "DVSA-USER-INBOX",
                "description": "用户收件箱",
                "trigger": "Lambda调用",
                "test_payload": {"user": "test-user"}
            },
            "admin_get_orders": {
                "function_name": "DVSA-ADMIN-GET-ORDERS",
                "description": "管理员获取订单",
                "trigger": "Lambda调用",
                "test_payload": {}
            },
            "admin_get_receipt": {
                "function_name": "DVSA-ADMIN-GET-RECEIPT",
                "description": "管理员获取收据",
                "trigger": "Lambda调用",
                "test_payload": {"year": "2024", "month": "01"}
            },
            "admin_update_orders": {
                "function_name": "DVSA-ADMIN-UPDATE-ORDERS",
                "description": "管理员更新订单",
                "trigger": "Lambda调用",
                "test_payload": {"orderId": "test-order", "status": "completed"}
            },
            "init_custom": {
                "function_name": "DVSA-INIT",
                "description": "初始化自定义资源",
                "trigger": "CloudFormation",
                "test_payload": {}
            }
        }
    
    def deploy_dvsa(self) -> bool:
        """部署DVSA应用"""
        print("🚀 开始部署DVSA...")

        # 检查是否已有SAM Local在运行
        if self._find_sam_process():
            print("✅ 检测到SAM Local已在运行，跳过部署步骤")
            self.test_results["deployment_status"]["existing_sam"] = {"success": True}
            return True

        # 检查端口是否被其他进程占用
        if not self._check_port_available(self.port):
            print(f"❌ 端口 {self.port} 被其他进程占用")
            print(f"   请停止占用端口的进程或使用其他端口")
            self.test_results["deployment_status"]["port_check"] = {
                "success": False,
                "error": f"Port {self.port} is already in use"
            }
            return False

        try:
            # 1. 构建应用
            print("  📦 构建SAM应用...")
            build_result = subprocess.run(
                ["sam", "build"],
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if build_result.returncode != 0:
                print(f"  ❌ 构建失败: {build_result.stderr}")
                self.test_results["deployment_status"]["build"] = {
                    "success": False,
                    "error": build_result.stderr
                }
                return False
            
            print("  ✅ 构建成功")
            self.test_results["deployment_status"]["build"] = {"success": True}
            
            # 2. 启动本地API（后台运行）
            print("  🌐 启动SAM Local API...")
            self.api_process = subprocess.Popen(
                ["sam", "local", "start-api", "--port", "3000"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待API启动
            print("  ⏳ 等待API启动...")
            time.sleep(10)
            
            # 验证API可访问性
            try:
                response = requests.get(f"{self.base_url}/", timeout=5)
                if response.status_code == 403:  # Missing Authentication Token
                    print("  ✅ API Gateway启动成功")
                    self.test_results["deployment_status"]["api"] = {"success": True}
                    return True
                else:
                    print(f"  ⚠️ API响应异常: {response.status_code}")
                    self.test_results["deployment_status"]["api"] = {
                        "success": False,
                        "error": f"Unexpected status code: {response.status_code}"
                    }
                    return False
            except Exception as e:
                print(f"  ❌ API连接失败: {str(e)}")
                self.test_results["deployment_status"]["api"] = {
                    "success": False,
                    "error": str(e)
                }
                return False
                
        except Exception as e:
            print(f"  💥 部署异常: {str(e)}")
            self.test_results["deployment_status"]["general"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def test_api_functions(self) -> Dict[str, Any]:
        """测试API Gateway直接路由的函数"""
        print("🧪 测试API Gateway函数...")

        results = {}

        for func_key, func_info in self.api_functions.items():
            print(f"  测试 {func_key} ({func_info['endpoint']})...")
            print(f"    描述: {func_info['description']}")

            try:
                # 准备请求头
                headers = {"Content-Type": "application/json"}

                # 对于需要认证的函数，添加模拟的Authorization头
                if func_info.get("requires_auth") or func_info.get("requires_admin"):
                    # 创建一个模拟的JWT token（用于测试函数是否正确处理认证）
                    mock_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QtdXNlciIsImV4cCI6OTk5OTk5OTk5OX0.test"
                    headers["Authorization"] = mock_token

                response = requests.post(
                    f"{self.base_url}{func_info['endpoint']}",
                    json=func_info["test_payload"],
                    headers=headers,
                    timeout=15
                )

                result = {
                    "function_name": func_info["function_name"],
                    "endpoint": func_info["endpoint"],
                    "description": func_info["description"],
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds(),
                    "response_body": response.text[:300],
                    "requires_auth": func_info.get("requires_auth", False),
                    "requires_admin": func_info.get("requires_admin", False)
                }

                # 分析响应状态
                if response.status_code == 200:
                    try:
                        result["parsed_response"] = response.json()
                        result["success"] = True
                        result["status"] = "正常工作"
                        print(f"    ✅ 成功: {response.status_code}")
                    except:
                        result["success"] = True
                        result["status"] = "响应解析失败但函数正常"
                        print(f"    ✅ 成功但响应解析失败: {response.status_code}")

                elif response.status_code in [400, 401, 403]:
                    result["success"] = True  # 认证/授权错误是预期的
                    if response.status_code == 401:
                        result["status"] = "需要有效认证"
                        print(f"    ✅ 认证检查正常: {response.status_code}")
                    elif response.status_code == 403:
                        result["status"] = "需要管理员权限"
                        print(f"    ✅ 权限检查正常: {response.status_code}")
                    else:
                        result["status"] = "请求参数错误"
                        print(f"    ⚠️ 参数错误: {response.status_code}")

                elif response.status_code == 500:
                    result["success"] = False
                    result["status"] = "内部服务器错误"
                    result["error_analysis"] = self._analyze_500_error(func_key, response.text)
                    print(f"    ❌ 内部错误: {response.status_code}")
                    print(f"      错误分析: {result['error_analysis']['likely_cause']}")

                elif response.status_code == 502:
                    result["success"] = False
                    result["status"] = "函数不可达"
                    print(f"    ❌ 函数不可达: {response.status_code}")

                else:
                    result["success"] = False
                    result["status"] = f"意外状态码: {response.status_code}"
                    print(f"    ⚠️ 意外状态码: {response.status_code}")

                results[func_key] = result
                time.sleep(1)  # 避免过快请求

            except Exception as e:
                print(f"    💥 异常: {str(e)}")
                results[func_key] = {
                    "function_name": func_info["function_name"],
                    "endpoint": func_info["endpoint"],
                    "description": func_info["description"],
                    "success": False,
                    "error": str(e),
                    "status": "连接异常"
                }

        self.test_results["api_function_tests"] = list(results.values())
        return results

    def _analyze_500_error(self, func_key: str, response_text: str) -> Dict[str, str]:
        """分析500错误的可能原因"""
        analysis = {
            "function": func_key,
            "likely_cause": "未知错误",
            "suggested_fix": "检查函数日志",
            "details": response_text[:200]
        }

        if func_key == "order":
            analysis.update({
                "likely_cause": "缺少有效的JWT认证token或Cognito用户池配置问题",
                "suggested_fix": "检查Authorization头格式，确保Cognito用户池正确配置，或使用有效的JWT token",
                "technical_details": "ORDER-MANAGER函数需要解析JWT token获取用户信息，本地环境可能缺少有效的Cognito配置"
            })
        elif func_key == "admin":
            analysis.update({
                "likely_cause": "缺少有效的用户ID或DynamoDB用户表中没有管理员用户",
                "suggested_fix": "在DynamoDB DVSA-USERS-DB表中创建管理员用户，或提供正确的userId参数",
                "technical_details": "ADMIN-SHELL函数需要验证用户是否为管理员，需要在用户表中查找用户记录"
            })

        return analysis
    
    def test_end_to_end_workflow(self) -> Dict[str, Any]:
        """测试完整的购物流程"""
        print("🛒 测试端到端购物流程...")
        
        workflow_results = {}
        
        # 步骤1: 计算购物车总价
        print("  步骤1: 计算购物车总价...")
        cart_items = [
            {"itemId": "item1", "quantity": 2, "price": 10.99},
            {"itemId": "item2", "quantity": 1, "price": 25.50}
        ]
        
        try:
            total_response = requests.post(
                f"{self.base_url}/total",
                json=cart_items,
                timeout=10
            )
            
            workflow_results["cart_total"] = {
                "success": total_response.status_code == 200,
                "status_code": total_response.status_code,
                "response": total_response.text[:100]
            }
            
            if total_response.status_code == 200:
                print("    ✅ 购物车总价计算成功")
            else:
                print(f"    ❌ 购物车总价计算失败: {total_response.status_code}")
                
        except Exception as e:
            print(f"    💥 购物车总价计算异常: {str(e)}")
            workflow_results["cart_total"] = {"success": False, "error": str(e)}
        
        # 步骤2: 处理支付
        print("  步骤2: 处理支付...")
        payment_data = {
            "ccn": "4532015112830366",  # 有效的测试信用卡号
            "exp": "12/25",
            "cvv": "123"
        }
        
        try:
            payment_response = requests.post(
                f"{self.base_url}/payment",
                json=payment_data,
                timeout=15
            )
            
            workflow_results["payment"] = {
                "success": payment_response.status_code == 200,
                "status_code": payment_response.status_code,
                "response": payment_response.text[:100]
            }
            
            if payment_response.status_code == 200:
                print("    ✅ 支付处理成功")
                try:
                    payment_result = payment_response.json()
                    confirmation_token = payment_result.get("confirmation_token")
                    if confirmation_token:
                        print(f"    💳 获得支付确认令牌: {confirmation_token}")
                        workflow_results["payment"]["confirmation_token"] = confirmation_token
                except:
                    pass
            else:
                print(f"    ❌ 支付处理失败: {payment_response.status_code}")
                
        except Exception as e:
            print(f"    💥 支付处理异常: {str(e)}")
            workflow_results["payment"] = {"success": False, "error": str(e)}
        
        # 步骤3: 创建订单（需要认证，测试认证机制）
        print("  步骤3: 测试订单创建（认证机制）...")
        order_data = {
            "action": "new",
            "cart-id": "test-cart-123",
            "items": cart_items
        }
        
        try:
            order_response = requests.post(
                f"{self.base_url}/order",
                json=order_data,
                timeout=10
            )
            
            workflow_results["order_creation"] = {
                "success": order_response.status_code in [401, 403, 500],  # 预期需要认证
                "status_code": order_response.status_code,
                "response": order_response.text[:100]
            }
            
            if order_response.status_code in [401, 403]:
                print("    ✅ 订单创建正确要求认证")
            elif order_response.status_code == 500:
                print("    ⚠️ 订单创建函数可达但有业务逻辑错误")
            else:
                print(f"    ⚠️ 订单创建意外响应: {order_response.status_code}")
                
        except Exception as e:
            print(f"    💥 订单创建异常: {str(e)}")
            workflow_results["order_creation"] = {"success": False, "error": str(e)}
        
        self.test_results["end_to_end_tests"] = workflow_results
        return workflow_results
    
    def test_internal_functions(self) -> Dict[str, Any]:
        """测试内部Lambda函数（通过SAM CLI直接调用）"""
        print("🔧 测试内部Lambda函数...")

        results = {}

        for func_key, func_info in self.internal_functions.items():
            if self.interrupted:
                break

            print(f"  测试 {func_key} ({func_info['function_name']})...")
            print(f"    描述: {func_info['description']}")
            print(f"    触发方式: {func_info['trigger']}")

            try:
                # 使用SAM CLI直接调用Lambda函数
                payload_file = f"/tmp/test_payload_{func_key}.json"
                with open(payload_file, 'w') as f:
                    json.dump(func_info["test_payload"], f)

                # 执行SAM CLI调用
                cmd = [
                    "sam", "local", "invoke",
                    func_info["function_name"],
                    "--event", payload_file,
                    "--no-event"
                ]

                start_time = time.time()
                result_process = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                end_time = time.time()

                # 清理临时文件
                try:
                    os.remove(payload_file)
                except:
                    pass

                result = {
                    "function_name": func_info["function_name"],
                    "description": func_info["description"],
                    "trigger": func_info["trigger"],
                    "return_code": result_process.returncode,
                    "response_time": round(end_time - start_time, 2),
                    "stdout": result_process.stdout[:500],
                    "stderr": result_process.stderr[:500]
                }

                if result_process.returncode == 0:
                    result["success"] = True
                    result["status"] = "正常工作"
                    print(f"    ✅ 成功调用")

                    # 尝试解析输出中的JSON响应
                    try:
                        # SAM CLI的输出通常包含函数返回值
                        lines = result_process.stdout.split('\n')
                        for line in lines:
                            if line.strip().startswith('{') and line.strip().endswith('}'):
                                result["parsed_response"] = json.loads(line.strip())
                                break
                    except:
                        pass

                else:
                    result["success"] = False
                    result["status"] = "调用失败"
                    print(f"    ❌ 调用失败: {result_process.returncode}")
                    if result_process.stderr:
                        print(f"      错误: {result_process.stderr[:100]}")

                results[func_key] = result
                time.sleep(0.5)

            except subprocess.TimeoutExpired:
                print(f"    ⏰ 调用超时")
                results[func_key] = {
                    "function_name": func_info["function_name"],
                    "description": func_info["description"],
                    "success": False,
                    "error": "调用超时",
                    "status": "超时"
                }
            except Exception as e:
                print(f"    💥 异常: {str(e)}")
                results[func_key] = {
                    "function_name": func_info["function_name"],
                    "description": func_info["description"],
                    "success": False,
                    "error": str(e),
                    "status": "异常"
                }

        self.test_results["internal_function_tests"] = list(results.values())
        return results
    
    def generate_comprehensive_report(self) -> str:
        """生成详细的测试报告"""
        # 计算API函数统计
        total_api_functions = len(self.api_functions)
        successful_api_functions = len([t for t in self.test_results["api_function_tests"] if t.get("success", False)])

        # 计算内部函数统计
        total_internal_functions = len(self.internal_functions)
        successful_internal_functions = len([t for t in self.test_results["internal_function_tests"] if t.get("success", False)])

        # 计算端到端测试统计
        workflow_success_count = len([t for t in self.test_results["end_to_end_tests"].values() if t.get("success", False)])
        total_workflow_steps = len(self.test_results["end_to_end_tests"])

        # 收集错误分析
        error_functions = []
        for test in self.test_results["api_function_tests"]:
            if not test.get("success", False):
                error_functions.append({
                    "function_name": test["function_name"],
                    "error_type": test.get("status", "未知错误"),
                    "analysis": test.get("error_analysis", {})
                })

        for test in self.test_results["internal_function_tests"]:
            if not test.get("success", False):
                error_functions.append({
                    "function_name": test["function_name"],
                    "error_type": test.get("status", "未知错误"),
                    "stderr": test.get("stderr", "")
                })

        # 生成修复建议
        fix_suggestions = self._generate_fix_suggestions(error_functions)

        self.test_results["summary"] = {
            "deployment_successful": all(
                status.get("success", False)
                for status in self.test_results["deployment_status"].values()
            ),
            "api_functions": {
                "total": total_api_functions,
                "successful": successful_api_functions,
                "success_rate": f"{(successful_api_functions/total_api_functions*100):.1f}%" if total_api_functions > 0 else "0%"
            },
            "internal_functions": {
                "total": total_internal_functions,
                "successful": successful_internal_functions,
                "success_rate": f"{(successful_internal_functions/total_internal_functions*100):.1f}%" if total_internal_functions > 0 else "0%"
            },
            "end_to_end_workflow": {
                "total_steps": total_workflow_steps,
                "successful_steps": workflow_success_count,
                "success_rate": f"{(workflow_success_count/total_workflow_steps*100):.1f}%" if total_workflow_steps > 0 else "0%"
            },
            "overall_health": {
                "total_functions": total_api_functions + total_internal_functions,
                "working_functions": successful_api_functions + successful_internal_functions,
                "error_functions": len(error_functions),
                "ready_for_attacks": successful_api_functions >= 2 and workflow_success_count >= 1
            },
            "error_analysis": error_functions,
            "fix_suggestions": fix_suggestions
        }

        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"report/dvsa_comprehensive_test_report_{timestamp}.json"

        os.makedirs("report", exist_ok=True)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)

        print(f"\n📊 详细测试报告已保存: {filename}")
        return filename

    def _generate_fix_suggestions(self, error_functions: List[Dict]) -> List[Dict]:
        """生成修复建议"""
        suggestions = []

        for error_func in error_functions:
            func_name = error_func["function_name"]

            if "ORDER-MANAGER" in func_name:
                suggestions.append({
                    "function": func_name,
                    "issue": "认证token解析失败",
                    "solution": "创建有效的Cognito用户和JWT token，或修改函数以支持本地测试模式",
                    "commands": [
                        "# 方法1: 创建测试用户",
                        "aws cognito-idp admin-create-user --user-pool-id <pool-id> --username test-user",
                        "# 方法2: 修改函数跳过认证（仅用于测试）"
                    ]
                })
            elif "ADMIN-SHELL" in func_name:
                suggestions.append({
                    "function": func_name,
                    "issue": "缺少管理员用户记录",
                    "solution": "在DynamoDB用户表中创建管理员用户记录",
                    "commands": [
                        "# 在DynamoDB DVSA-USERS-DB表中插入管理员用户",
                        "aws dynamodb put-item --table-name DVSA-USERS-DB --item '{\"userId\":{\"S\":\"admin-user\"},\"isAdmin\":{\"BOOL\":true}}'"
                    ]
                })
            elif "DynamoDB" in error_func.get("stderr", ""):
                suggestions.append({
                    "function": func_name,
                    "issue": "DynamoDB表不存在或权限不足",
                    "solution": "确保DynamoDB表已创建且Lambda函数有访问权限",
                    "commands": [
                        "# 检查DynamoDB表状态",
                        "aws dynamodb list-tables",
                        "aws dynamodb describe-table --table-name DVSA-ORDERS-DB"
                    ]
                })

        return suggestions
    
    def cleanup(self):
        """清理资源"""
        print("🧹 清理资源...")
        if self.api_process and self.api_process.poll() is None:
            print("   终止SAM Local进程...")
            try:
                self.api_process.terminate()
                self.api_process.wait(timeout=5)
                print("   ✅ SAM Local进程已终止")
            except subprocess.TimeoutExpired:
                print("   ⚠️ 强制终止SAM Local进程...")
                self.api_process.kill()
                self.api_process.wait()
            except Exception as e:
                print(f"   ⚠️ 清理进程时出错: {str(e)}")

        # 清理临时文件等
        try:
            # 这里可以添加其他清理逻辑
            pass
        except Exception as e:
            print(f"   ⚠️ 清理资源时出错: {str(e)}")

def main():
    print("🚀 DVSA 部署和完整流程测试")
    print("=" * 60)

    tester = DVSADeploymentTester()

    try:
        # 1. 部署DVSA
        if not tester.deploy_dvsa():
            print("❌ 部署失败，无法继续测试")
            return

        if tester.interrupted:
            return

        print()

        # 2. 测试API Gateway函数
        print("第一阶段: API Gateway函数测试")
        print("-" * 40)
        tester.test_api_functions()
        print()

        if tester.interrupted:
            return

        # 3. 测试内部Lambda函数
        print("第二阶段: 内部Lambda函数测试")
        print("-" * 40)
        tester.test_internal_functions()
        print()

        if tester.interrupted:
            return

        # 4. 测试端到端流程
        print("第三阶段: 端到端业务流程测试")
        print("-" * 40)
        tester.test_end_to_end_workflow()
        print()

        if tester.interrupted:
            return

        # 5. 生成详细报告
        print("=" * 60)
        report_file = tester.generate_comprehensive_report()

        # 6. 显示详细摘要
        summary = tester.test_results["summary"]
        print(f"📈 详细测试摘要:")
        print(f"   部署状态: {'✅ 成功' if summary['deployment_successful'] else '❌ 失败'}")

        api_stats = summary["api_functions"]
        print(f"   API Gateway函数: {api_stats['successful']}/{api_stats['total']} ({api_stats['success_rate']})")

        internal_stats = summary["internal_functions"]
        print(f"   内部Lambda函数: {internal_stats['successful']}/{internal_stats['total']} ({internal_stats['success_rate']})")

        workflow_stats = summary["end_to_end_workflow"]
        print(f"   端到端流程: {workflow_stats['successful_steps']}/{workflow_stats['total_steps']} ({workflow_stats['success_rate']})")

        overall_stats = summary["overall_health"]
        print(f"   总体健康度: {overall_stats['working_functions']}/{overall_stats['total_functions']} 函数正常工作")

        if overall_stats["error_functions"] > 0:
            print(f"   ⚠️ 发现 {overall_stats['error_functions']} 个函数存在问题")
            print(f"   📋 修复建议数量: {len(summary['fix_suggestions'])}")

            # 显示主要错误和修复建议
            print(f"\n🔧 主要问题和修复建议:")
            for suggestion in summary["fix_suggestions"][:3]:  # 只显示前3个
                print(f"   • {suggestion['function']}: {suggestion['issue']}")
                print(f"     解决方案: {suggestion['solution']}")

        print(f"   攻击测试就绪: {'✅ 是' if overall_stats['ready_for_attacks'] else '❌ 否'}")

        if overall_stats['ready_for_attacks']:
            print("\n🎯 DVSA部署成功，可以开始攻击测试！")
            print("   运行通用攻击: python scripts/control_flow_attack.py")
            print("   运行特定攻击: python scripts/dvsa_specific_attacks.py")
            print(f"   查看详细报告: {report_file}")
        else:
            print("\n⚠️ 部分功能异常，建议先修复问题再进行攻击测试")
            print(f"   查看详细错误分析: {report_file}")
            print("   或继续进行攻击测试以验证漏洞利用能力")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
