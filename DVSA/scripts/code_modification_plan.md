# DVSA 源码修改方案

## 📋 修改目的
为了准确验证攻击效果，需要在DVSA源码中添加调试输出，但不修改已有的业务逻辑。

## 🎯 修改计划

### 1. ORDER-MANAGER 函数修改 (关键)

**文件**: `backend/functions/order-manager/order-manager.js`

**修改位置和内容**:

#### 修改1: 在反序列化前后添加日志
```javascript
// 在第10行前添加
console.log('[DEBUG] Raw event.body:', event.body);
console.log('[DEBUG] Raw event.headers:', event.headers);

// 修改第10-11行为:
console.log('[DEBUG] Starting deserialization...');
var req = serialize.unserialize(event.body);
console.log('[DEBUG] Deserialized req:', JSON.stringify(req));
var headers = serialize.unserialize(event.headers);
console.log('[DEBUG] Deserialized headers:', JSON.stringify(headers));
```

#### 修改2: 在action处理前添加日志
```javascript
// 在第38行后添加
console.log('[DEBUG] Processing action:', action);
console.log('[DEBUG] User:', user);
console.log('[DEBUG] isAdmin:', isAdmin);
```

#### 修改3: 在Lambda调用前后添加日志
```javascript
// 在第148行前添加
console.log('[DEBUG] Calling Lambda function:', functionName);
console.log('[DEBUG] Payload:', JSON.stringify(payload));

// 在第155行后添加
console.log('[DEBUG] Lambda call initiated for:', functionName);
```

#### 修改4: 在catch块中添加详细错误日志
```javascript
// 修改第183-185行为:
catch (e){
    console.log('[ERROR] Exception in order-manager:', e);
    console.log('[ERROR] Stack trace:', e.stack);
    console.log('[ERROR] Event body that caused error:', event.body);
    
    // 返回错误响应而不是静默失败
    const error_response = {
        statusCode: 500,
        headers: {
            "Access-Control-Allow-Origin" : "*"
        },
        body: JSON.stringify({
            "status": "error", 
            "message": "Internal server error",
            "debug": e.message
        })
    };
    callback(null, error_response);
}
```

### 2. ADMIN-SHELL 函数修改

**文件**: `backend/functions/admin/admin_shell.js`

**修改内容**:
```javascript
// 在函数开始处添加
console.log('[DEBUG] Admin shell called with event:', JSON.stringify(event));

// 在用户验证处添加
console.log('[DEBUG] Checking admin privileges for user:', userId);

// 在命令执行前添加
console.log('[DEBUG] Executing admin command:', cmd);
```

### 3. PAYMENT 函数修改

**文件**: `backend/functions/processing/payment_processing.py`

**修改内容**:
```python
# 在函数开始处添加
print(f"[DEBUG] Payment processing called with: {event}")

# 在信用卡验证处添加
print(f"[DEBUG] Validating credit card: ****{ccn[-4:]}")

# 在返回前添加
print(f"[DEBUG] Payment result: {result}")
```

## 🔧 修改执行步骤

### 步骤1: 备份原始文件
```bash
cp backend/functions/order-manager/order-manager.js backend/functions/order-manager/order-manager.js.backup
cp backend/functions/admin/admin_shell.js backend/functions/admin/admin_shell.js.backup
cp backend/functions/processing/payment_processing.py backend/functions/processing/payment_processing.py.backup
```

### 步骤2: 应用修改
使用str-replace-editor工具逐一应用上述修改

### 步骤3: 重新部署
```bash
sam build
sam local start-api --port 3000
```

### 步骤4: 验证修改效果
运行攻击验证脚本，检查日志输出

### 步骤5: 分析攻击证据
基于详细的日志输出判断攻击是否真正成功

## ⚠️ 注意事项

1. **不修改业务逻辑**: 只添加日志输出，不改变函数的原有行为
2. **保留原始文件**: 所有修改前都要备份原始文件
3. **详细记录**: 每个修改都要在此文档中详细记录
4. **可回滚**: 确保可以随时恢复到原始状态

## 📊 预期效果

修改后，我们应该能够看到:
1. 反序列化过程的详细日志
2. 恶意代码执行的证据
3. Lambda函数调用的详细信息
4. 具体的错误信息和堆栈跟踪

这将帮助我们准确判断攻击是否成功，而不是仅仅依赖HTTP状态码。

## 🎯 验证标准

攻击成功的证据包括:
1. 日志中出现恶意代码执行的痕迹
2. 看到非预期的Lambda函数调用
3. 控制台输出中出现攻击payload中的标识符
4. 错误日志显示反序列化执行了恶意代码

---
*修改方案创建时间: 2025-07-14*
*目的: 准确验证控制流篡改攻击效果*
