{"timestamp": "2025-07-14T14:25:24.564094", "working_functions": [{"function_name": "GetCartTotal", "endpoint": "/total", "status": "WORKING", "test_cases": [{"test_name": "空购物车数组", "status_code": 200, "response_body": "{\"status\": \"ok\", \"total\": 0.0, \"missing\": {}}", "response_time": 6.81, "success": true, "expected_success": true, "parsed_response": {"status": "ok", "total": 0.0, "missing": {}}}, {"test_name": "单个商品数组", "status_code": 200, "response_body": "{\"status\": \"error\", \"message\": \"Item could not be found in database\"}", "response_time": 6.9, "success": true, "expected_success": true, "parsed_response": {"status": "error", "message": "Item could not be found in database"}}, {"test_name": "多个商品数组", "status_code": 200, "response_body": "{\"status\": \"error\", \"message\": \"Item could not be found in database\"}", "response_time": 6.3, "success": true, "expected_success": true, "parsed_response": {"status": "error", "message": "Item could not be found in database"}}, {"test_name": "购物车字典格式", "status_code": 502, "response_body": "{\"message\":\"Internal server error\"}\n", "response_time": 0.39, "success": false, "expected_success": false}], "success_rate": "3/4"}, {"function_name": "AdminShell", "endpoint": "/admin", "status": "PARTIALLY_WORKING", "test_cases": [{"test_name": "基础请求", "status_code": 500, "response_body": "{\"message\":\"Internal server error\"}\n", "success": true, "function_reachable": true}, {"test_name": "空请求", "status_code": 502, "response_body": "{\"message\":\"Internal server error\"}\n", "success": false, "function_reachable": false}], "success_rate": "1/2"}, {"function_name": "PaymentProcessor", "endpoint": "/payment", "status": "WORKING", "test_result": {"status_code": 200, "response": {"status": 120, "confirmation_token": "oA6t009h2iPe"}, "attempts": 2}}], "summary": {"total_functions_tested": 3, "fully_working": 2, "partially_working": 1, "not_working": 0, "usable_for_attacks": 3}}